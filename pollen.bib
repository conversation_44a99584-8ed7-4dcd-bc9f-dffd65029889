@phdthesis{BaoXiaoDongJiYuGuangXianGongJuJiaoLaManGuangPuJiShuDeDuoChangJingZhiNengJianCeFangFaYanJiu2025,
  type = {{博士学位论文}},
  title = {{基于光纤共聚焦拉曼光谱技术的多场景智能检测方法研究}},
  author = {{包晓栋}},
  year = {2025},
  address = {北京},
  doi = {10.27522/d.cnki.gkcgs.2025.000016},
  abstract = {拉曼光谱是一种能提供分子振动信息的光谱技术,凭借其非侵入、快速、无损的特点,能准确地提供分子的指纹信息,以实现对物质的识别和鉴定,目前已被应用到生物医学、材料科学、工业检测、地球科学等广泛的研究领域。拉曼光谱仪作为检测拉曼光谱的重要工具,目前主要有共聚焦式和光纤式两种。但由于各个领域检测场景的不同,单一制式的光谱仪往往无法满足检测的多样性需求,而导致研发和采购成本的提高。同时,复杂的应用场景也给传统的光谱分析方法和识别算法带来了新的挑战。针对这一系列问题,本文提出``硬件基座+应用场景+智能算法''的协同研究方法,通过一套高检测自由度的拉曼光谱检测系统,结合人工智能算法,解决多场景下的检测问题。文本的主要研究内容如下:(1)针对拉曼检测模式多样性的需求,提出将共聚焦式和光纤式拉曼光谱仪的特点结合,通过在光路中引入反谐振空芯光纤,将共聚焦拉曼光谱仪的检测端与光谱仪主机解耦,实现了``单主机+自由检测端''的硬件模式,提高了检测端的空间自由度。反谐振空芯光纤实现了单光纤传输激发光和拉曼信号,且低拉曼背景特性使其在使用过程中无需任何尖端处理。与传统光纤拉曼不同,本系统可以搭配现有的高性能显微物镜使用,实现了显微检测模式和探头检测模式的自由切换,且均具备共聚焦检测能力,能满足各种研究场景的多样化智能检测需求。(2)面向跨仪器生物类拉曼光谱检测的场景,针对拉曼光谱仪的台间差异导致分类效果差的问题,使用更换不同反谐振空芯光纤的方法模拟了采集过程,通过数据降维可视化和传统分类算法的跨数据集测试证明了台间差异的存在,并提出一种基于孪生网络的小样本对比学习算法,利用模块化设计思路,实现不同光谱编码器的自由插拔,在训练集数据绝对数量较少的情况下,对比了Res Net、Transformer和LSTM三种不同编码器的分类效果,取得了远超传统光谱对比算法的准确率,实现了跨仪器生物类拉曼光谱的分类。(3)面向生物制药工业的颗粒物检测场景,针对开放空间的未知类别颗粒物拉曼光谱无法识别的问题,提出一种基于Open Max的开集识别及动态类增量的深度学习方法,通过已知分类数据的激活向量拟合Weibull分布,并重新构建带未知分类的Open Max函数预测分类概率,实现了对未知颗粒物拉曼光谱的高灵敏性识别。并提出使用多种策略的模型微调方法进行类增量学习,在大规模参数和小规模参数的微调任务中均取得了较高的分类准确率,实现了工业场景中模型...},
  collaborator = {{李备}},
  langid = {chinese},
  school = {中国科学院大学（中国科学院长春光学精密机械与物理研究所）},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/ALP9GNK6/基于光纤共聚焦拉曼光谱技术的多场景智能检测方法研究_包晓栋.pdf}
}

@article{brdarExplainableAIUnveiling2023,
  title = {Explainable {{AI}} for Unveiling Deep Learning Pollen Classification Model Based on Fusion of Scattered Light Patterns and Fluorescence Spectroscopy},
  author = {Brdar, Sanja and Pani{\'c}, Marko and Matavulj, Predrag and Stankovi{\'c}, Mira and Bartoli{\'c}, Dragana and {\v S}ikoparija, Branko},
  year = {2023},
  month = feb,
  journal = {Scientific Reports},
  volume = {13},
  number = {1},
  pages = {3205},
  publisher = {Nature Publishing Group},
  issn = {2045-2322},
  doi = {10.1038/s41598-023-30064-6},
  urldate = {2025-02-16},
  abstract = {Pollen monitoring have become data-intensive in recent years as real-time detectors are deployed to classify airborne pollen grains. Machine learning models with a focus on deep learning, have an essential role in the pollen classification task. Within this study we developed an explainable framework to unveil a deep learning model for pollen classification. Model works on data coming from single particle detector (Rapid-E) that records for each particle optical fingerprint with scattered light and laser induced fluorescence. Morphological properties of a particle are sensed with the light scattering process, while chemical properties are encoded with fluorescence spectrum and fluorescence lifetime induced by high-resolution laser. By utilizing these three data modalities, scattering, spectrum, and lifetime, deep learning-based models with millions of parameters are learned to distinguish different pollen classes, but a proper understanding of such a black-box model decisions demands additional methods to employ. Our study provides the first results of applied explainable artificial intelligence (xAI) methodology on the pollen classification model. Extracted knowledge on the important features that attribute to the predicting particular pollen classes is further examined from the perspective of domain knowledge and compared to available reference data on pollen sizes, shape, and laboratory spectrofluorometer measurements.},
  copyright = {2023 The Author(s)},
  langid = {english},
  lccn = {2},
  keywords = {/unread,Computer science,Machine learning,Pollen},
  annotation = {TLDR: This study provides the first results of applied explainable artificial intelligence (xAI) methodology on the pollen classification model, and works on data coming from single particle detector that records for each particle optical fingerprint with scattered light and laser induced fluorescence.},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/AFR4U58V/Brdar 等 - 2023 - Explainable AI for unveiling deep learning pollen classification model based on fusion of scattered.pdf}
}

@phdthesis{CaoYuanXinGaiJinHuaShouFenSuanFaJiQiZaiTuXiangFenGeZhongDeYingYongYanJiu2024,
  type = {{硕士学位论文}},
  title = {{改进花授粉算法及其在图像分割中的应用研究}},
  author = {曹渊鑫},
  year = {2024},
  address = {西安},
  abstract = {花授粉算法(Flower Pollination Algorithm,FPA)是一种简单高效的元启发式算法。其有效模拟了显花植物的授粉行为,并且具有设置参数少、鲁棒性强、结构简单、寻优效率高等优点,但FPA仍存在迭代后期收敛速度慢、脱离局部最优能力较弱等不足。因此,本研究提出两种改进的FPA,并将改进后的FPA应用于图像分割。具体工作如下: (1)针对合成孔径雷达(Synthetic Aperture Radar,SAR)图像中相干斑噪声导致SAR图像的分割难度增加的问题,提出一种基于改进花授粉算法(Improved FPA with Sobol sequence initital strategy and differential evolution mutation strategy,SDEFPA)的 SAR 图像阈值分割方法。通过搜索域变换策略以提高算法搜索效率,采用Sobol序列初始化种群策略提高种群均匀性及多样性,引入DE/current-to-best/1变异策略弥补FPA可能陷入局部最优的不足。CEC 2017测试函数集上的结果表明SDEFPA相较于对比算法拥有更好的求解精度和更快的收敛速度。将二维灰熵作为SDEFPA的适应度函数,分割阈值可视为灰数,利用SDEFPA完成灰数的白化过程,得出最佳分割阈值。图像分割实验结果表明,相比于多策略融合鲸鱼优化算法的二维最大熵法、基于改进自适应差分演化算法的二维Otsu法,所提分割方法分割速度更快、所分割图像质量更高。 (2)针对K-means算法对初始聚类中心敏感,其处理图像分割等较复杂问题时易陷入局部最优导致难以获得满意分割结果的问题,提出一种将改进花授粉算法(Enhanced FPA with the new local search strategy and the adaptive cauchy-gauss mutation strategy,NMFPA)与K-means算法相结合的图像分割方法。首先通过新局部搜索策略引导花粉配子个体进化方向并增加种群搜索范围有效提升了算法的收敛速度及搜索精度;通过自适应柯西-高斯变异策略扩大了种群搜索范围,降低了算法陷入局部最优解的概率。CEC 2017和CEC 2021测试函数集上的结果验证了改进花授粉方法寻优精度高、稳定性好。最后将NMFPA用于优化K-means算法,通过与FPA与K-means算法相结合...},
  langid = {chinese},
  school = {西安理工大学},
  keywords = {,/unread,DE/current-to-best/1,K-means},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/W4BWQSHP/改进花授粉算法及其在图像分割中的应用研究_曹渊鑫.pdf}
}

@article{eryilmazPaperbasedMultiplexedSerological2024,
  title = {A Paper-Based Multiplexed Serological Test to Monitor Immunity against {{SARS-COV-2}} Using Machine Learning},
  author = {Eryilmaz, Merve and Goncharov, Artem and Han, Gyeo-Re and Joung, Hyou-Arm and Ballard, Zachary S. and Ghosh, Rajesh and Zhang, Yijie and Di Carlo, Dino and Ozcan, Aydogan},
  year = {2024},
  month = jul,
  journal = {ACS Nano},
  volume = {18},
  number = {26},
  pages = {16819--16831},
  issn = {1936-0851},
  doi = {10.1021/acsnano.4c02434},
  urldate = {2025-03-10},
  abstract = {The rapid spread of SARS-CoV-2 caused the COVID-19 pandemic and accelerated vaccine development to prevent the spread of the virus and control the disease. Given the sustained high infectivity and evolution of SARS-CoV-2, there is an ongoing interest in developing COVID-19 serology tests to monitor population-level immunity. To address this critical need, we designed a paper-based multiplexed vertical flow assay (xVFA) using five structural proteins of SARS-CoV-2, detecting IgG and IgM antibodies to monitor changes in COVID-19 immunity levels. Our platform not only tracked longitudinal immunity levels but also categorized COVID-19 immunity into three groups: protected, unprotected, and infected, based on the levels of IgG and IgM antibodies. We operated two xVFAs in parallel to detect IgG and IgM antibodies using a total of 40 {$\mu$}L of human serum sample in {$<$}20 min per test. After the assay, images of the paper-based sensor panel were captured using a mobile phone-based custom-designed optical reader and then processed by a neural network-based serodiagnostic algorithm. The serodiagnostic algorithm was trained with 120 measurements/tests and 30 serum samples from 7 randomly selected individuals and was blindly tested with 31 serum samples from 8 different individuals, collected before vaccination as well as after vaccination or infection, achieving an accuracy of 89.5\%. The competitive performance of the xVFA, along with its portability, cost-effectiveness, and rapid operation, makes it a promising computational point-of-care (POC) serology test for monitoring COVID-19 immunity, aiding in timely decisions on the administration of booster vaccines and general public health policies to protect vulnerable populations.},
  langid = {english},
  lccn = {1},
  keywords = {/unread},
  annotation = {JCR分区: Q1\\
中科院分区升级版: 材料科学1区\\
影响因子: 15.8\\
5年影响因子: 16.2\\
EI: 是\\
TLDR: The competitive performance of the xVFA, along with its portability, cost-effectiveness, and rapid operation, makes it a promising computational point-of-care (POC) serology test for monitoring COVID-19 immunity, aiding in timely decisions on the administration of booster vaccines and general public health policies to protect vulnerable populations.},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/JRRSM2Y6/Eryilmaz 等 - 2024 - A Paper-Based Multiplexed Serological Test to Monitor Immunity against SARS-COV-2 Using Machine Lear.pdf}
}

@phdthesis{FuDuoMinJiYuJuanJiShenJingWangLuoDeHuaFenTuXiangShiBieFangFaYanJiu2022,
  type = {{硕士学位论文}},
  title = {{基于卷积神经网络的花粉图像识别方法研究}},
  author = {付多民},
  year = {2022},
  address = {唐山},
  abstract = {花粉的识别在孢粉学、法庭科学和古气候重建等领域中都发挥着重要的作用。目前,大部分花粉图像的自动化识别精度较低,且花粉图像识别模型的训练时间较长。因此,实现花粉图像识别模型的高精度和快速识别具有重要的意义。基于公开花粉图像数据集POLEN23E和POLLEN73S,引入卷积神经网络对花粉图像进行分类识别,重点研究了花粉图像的数据增强、参数优化方法以及花粉图像识别模型的构建。主要研究内容如下:1)为了保证实验的可靠性与真实性,采用随机选取的方式,使用K折交叉验证的方法对花粉图像数据进行划分,并采用图像翻转、色彩调节、Mix Up和Grid Mask等数据增强的方法对原始图像进行处理,抑制模型的过拟合情况,从而提高模型的测试精度。2)针对花粉图像清晰度低、形状相近等特性所导致的识别精度较低的问题,提出一种基于动态高效网络的花粉图像识别模型。首先,在Image Net数据集上使用Noisy Student方法对Efficient Net进行预训练;然后,将训练后的权重迁移到花粉识别模型中;最后,引入动态学习率提升模型的识别精度。仿真结果表明,基于动态高效网络的花粉图像识别模型具有很高的分类精度。3)从模型的训练时间角度出发,提出了一种基于改进残差网络的花粉图像识别模型。首先,通过添加Dropout层等方法,对Res Net50进行模型微调;然后,将模型中标准的卷积替换为空洞卷积;最后,将模型中Relu激活函数替换为Mish激活函数。仿真结果表明,基于改进残差网络的花粉图像识别模型能够使用较少的训练时间,并达到令人满意的分类效果。4)为了更好地满足用户对于简约性、快捷性和人性化的需求。因此,设计了具有较好的交互效果的花粉图像识别平台。该平台软件主要包括用户注册登录、花粉图像数据选择、花粉图像识别模型训练、花粉图像识别模型测试验证、花粉图像识别等5个模块。通过实际验证,该平台为花粉图像的自动化识别提供了便捷的识别工具。图35幅;表11个;参55篇。},
  langid = {chinese},
  school = {华北理工大学},
  keywords = {,/unread},
  annotation = {citation: 3},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/YK65QMLX/基于卷积神经网络的花粉图像识别方法研究_付多民.pdf}
}

@misc{GeometricDataAugmentations,
  title = {Geometric Data Augmentations to Mitigate Distribution Shifts in Pollen Classification from Microscopic Images},
  journal = {ar5iv},
  urldate = {2025-02-16},
  abstract = {Distribution shifts are characterized by differences between the training and test data distributions. They can significantly reduce the accuracy of machine learning models deployed in real-world scenarios. This paper {\dots}},
  howpublished = {https://ar5iv.labs.arxiv.org/html/2311.11029},
  langid = {english},
  keywords = {/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/3I2B37PZ/2311.html}
}

@phdthesis{GuoXinYuJiYuXianWeiTuXiangDeQiChuanHuaFenZiDongJianCeXiTongYanJiuYuShiXian2022,
  type = {{硕士学位论文}},
  title = {{基于显微图像的气传花粉自动检测系统研究与实现}},
  author = {郭昕雨},
  year = {2022},
  address = {天津},
  langid = {chinese},
  school = {天津大学},
  keywords = {/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/8VNJFVMK/基于显微图像的气传花粉自动检测系统研究与实现_郭昕雨.pdf}
}

@phdthesis{HanLiPingHuaFenTuXiangWenLiTeZhengTiQuFangFaDeYanJiu2019,
  type = {{硕士学位论文}},
  title = {{花粉图像纹理特征提取方法的研究}},
  author = {韩丽萍},
  year = {2019},
  address = {南京},
  abstract = {花粉颗粒的分类识别在花粉过敏控制、刑事侦查、石油勘探以及古气候重建等领域有着重要应用。但传统的花粉颗粒分类识别方法主要是依靠显微镜的人工目视检查,需要操作者具有丰富的孢粉形态学专业知识,鉴别过程耗时费力且易受操作者主观意识影响,准确度普遍不高。鉴于显微镜下的花粉图像有着跟普通图像类似的结构、纹理特征,利用计算机对花粉颗粒进行分类识别已经成为花粉鉴别的有效手段。但现有的花粉图像分类识别方法仍存在些许不足,主要包括以下两方面:现有描述子大多对噪声敏感、对花粉图像的旋转缩放没有较好的鲁棒性;多数描述子将多种特征融合,旨在利用不同特征的优点来构建花粉图像的最优表示,但这也大大增加了算法的时间复杂度,不利于花粉图像的实际分类识别。针对以上问题,本文对花粉图像的纹理特征提取方法进行研究,主要的研究内容包括:(1)针对传统局部二进制模式(LBP)有着对噪声敏感、对图像旋转变化的鲁棒性不高等问题,对传统局部二进制模式进行改进,提出一种基于主梯度编码的局部二进制模式,并将其应用于花粉图像的分类识别。该方法首先计算图像块在主梯度方向上的梯度幅值;其次,分别计算图像块的径向、角向以及复合梯度差;然后,根据各图像块的梯度差进行二进制编码,采用自适应权重分配策略为二进制编码自适应分配权重,并计算花粉图像在径向、角向以及复合方向上的LBP特征直方图;最后,将不同尺度下的纹理特征直方图融合,将融合特征用于花粉图像的分类识别。实验结果表明,该方法对花粉图像的噪声、旋转和缩放具有较好的鲁棒性。(2)针对花粉图像的纹理变化范围相较于普通纹理图像较小,过宽的量化区间难以捕捉不同花粉图像的细微纹理差异的问题,提出一种局部十进制模式(LDP),并将其应用于花粉图像的分类识别。该方法通过增加量化区间的数量、缩小量化区间的范围来捕获花粉图像的细微纹理差异,首先将花粉图像的梯度图像分解到8个方向,找出最大、最小以及中位梯度方向;其次,计算像素块在各梯度方向上的梯度幅值;然后,根据各量化区间内的像素块的数量进行十进制编码;最后,计算最大、最小以及中位梯度方向上的LDP特征直方图,将融合的特征用于花粉图像的分类识别。实验结果表明,该方法的平均正确识别率能达到90\%以上,且识别效率也高于部分对比方法。},
  langid = {chinese},
  school = {南京信息工程大学},
  keywords = {,/unread},
  annotation = {citation: 1},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/R5TFUFYF/花粉图像纹理特征提取方法的研究_韩丽萍.pdf}
}

@phdthesis{HaoJianLiTaiYuanShiXiaQiuJiQiChuanHuaFenJianCeJiGuoMinXingBiYanHuanZheBianYingYuanPuFenXi2023,
  type = {{硕士学位论文}},
  title = {{太原市夏秋季气传花粉监测及过敏性鼻炎患者变应原谱分析}},
  author = {郝建利},
  year = {2023},
  address = {太原},
  abstract = {目的:明确太原市夏秋季气传花粉的种类及播散规律及太原市夏秋季过敏性鼻炎患者过敏原分布情况;明确花粉与气象因子及过敏性鼻炎患者变应原检测人次的关系。方法:1.花粉采集将花粉采样设备(Durham取样器)放置在山西医科大学第一医院门诊部7楼楼顶,距地面约21米,地理位置位于东经112{$^\circ$}33{$\prime$}0{${''}$},北纬37{$^\circ$}51{$\prime$}14{${''}$},自2022年7月21日至2022年10月20日采用重力沉降法连续收集花粉。每天上午8点于采样器放置均匀涂有粘附剂的载玻片两张,至次日上午8点取片,使其全天暴露在空气中,取片后Callberla染色液染色加盖20mm{\texttimes}20mm盖玻片,光学显微镜下进行花粉颗粒的识别与计数,鉴别方法参照乔秉善撰写的《中国气传花粉和植物彩色图谱》。同时记录山西气象局官网每日预报的气象信息,包括温度、湿度、风力等气象数据。统计我院变态反应科同期变应原检测人数和变应原阳性人次,并记录到excel表中。2.变应原特异性Ig E抗体监测采集疑似过敏性鼻炎(allergic rhinitis,AR)患者空腹静脉血4ml,设置离心机3500 r/min,离心10min制备血清,采用全自动免疫印迹仪,根据吸入性和食入性变应原特异性Ig E抗体检测试剂盒(欧蒙印迹法)体外半定量检测受试者20种变应原s Ig E水平,其中包括10种吸入性变应原:(ts20)树木组合(柳树/榆树/杨树)、(w1)豚草、(w6)艾蒿、(dsl)室内尘螨组合(粉尘螨/屋尘螨)、(h1)屋尘、(e1)猫毛、(e2)狗上皮、(i6)德国蟑螂、(msl)点青霉/烟曲霉/分枝孢霉/交链孢霉、(u80)葎草;10种食入性变应原:(f1)蛋清、(f2)牛奶、(f13)花生、(f14)黄豆、(f27)牛肉、(f88)羊肉、(fs33)海洋鱼类组合(龙虾/鳕鱼/扇贝)、(f24)虾、(f23)蟹、(fs34)淡水鱼组合(鲑鱼/鲈鱼/鲤鱼),检测方法及步骤严格遵照说明书完成。所有操作均由同一名专业技师执行。结果:1.在为期92天(2022年7月21日至2022年10月20日)的气传花粉连续曝片中,共收集到花粉17118粒,其中曝片镜检出的花粉17107粒,鉴定到14科10属4种,未识别的花粉11粒。太原市夏秋季气传花粉飘散以杂草和禾本类为主,主要包括蒿属(66.62\%)、大麻/葎草属(17.79\%)、国槐(8.18\%)、藜/苋科(2.83\%)、禾本科(2.11\%),累计总量占花粉总...},
  langid = {chinese},
  school = {山西医科大学},
  keywords = {,/unread,IgE},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BYP8PXQR/太原市夏秋季气传花粉监测及过敏性鼻炎患者变应原谱分析_郝建利.pdf}
}

@article{hengSmartMaskExhaled2024,
  title = {A Smart Mask for Exhaled Breath Condensate Harvesting and Analysis},
  author = {Heng, Wenzheng and Yin, Shukun and Min, Jihong and Wang, Canran and Han, Hong and Shirzaei Sani, Ehsan and Li, Jiahong and Song, Yu and Rossiter, Harry B. and Gao, Wei},
  year = {2024},
  month = aug,
  journal = {Science},
  volume = {385},
  number = {6712},
  pages = {954--961},
  issn = {0036-8075, 1095-9203},
  doi = {10.1126/science.adn6471},
  urldate = {2025-02-09},
  abstract = {Recent respiratory outbreaks have garnered substantial attention, yet most respiratory monitoring remains confined to physical signals. Exhaled breath condensate (EBC) harbors rich molecular information that could unveil diverse insights into an individual's health. Unfortunately, challenges related to sample collection and the lack of on-site analytical tools impede the widespread adoption of EBC analysis. Here, we introduce EBCare, a mask-based device for real-time in situ monitoring of EBC biomarkers. Using a tandem cooling strategy, automated microfluidics, highly selective electrochemical biosensors, and a wireless reading circuit, EBCare enables continuous multimodal monitoring of EBC analytes across real-life indoor and outdoor activities. We validated EBCare's usability in assessing metabolic conditions and respiratory airway inflammation in healthy participants, patients with chronic obstructive pulmonary disease or asthma, and patients after COVID-19 infection.           ,              Editor's summary                            A challenge in some health care settings is the prompt acquisition of the appropriate data to be able to diagnose a changing medical situation and enable timely intervention. One common example is the assessment of blood glucose levels in people with diabetes and subsequent administration of insulin, which in recent years has moved from periodic snapshots to continuous monitoring. Heng               et al               . developed a smart face mask integrated with a self-cooling strategy, automated microfluidics, and biosensors for wearable exhaled breath condensate sampling and analysis of metabolites. The authors show the potential value of this mask for the acquisition of data for healthy participants and patients with chronic obstructive pulmonary disease, asthma, and post--COVID-19 infection in pilot human trials. ---Marc S. Lavine},
  langid = {english},
  lccn = {1},
  keywords = {/unread},
  annotation = {JCR分区: Q1\\
中科院分区升级版: 综合性期刊1区\\
影响因子: 44.7\\
5年影响因子: 50.3\\
中科院升级版Top分区: 综合性期刊TOP\\
TLDR: EBCare is introduced, a mask-based device for real-time in situ monitoring of EBC biomarkers that validated EBCare's usability in assessing metabolic conditions and respiratory airway inflammation in healthy participants, patients with chronic obstructive pulmonary disease or asthma, and patients after COVID-19 infection.},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/2LJQFMJJ/science.adn6471_sm.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/3K6FPTWJ/Heng 等 - 2024 - A smart mask for exhaled breath condensate harvesting and analysis.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/5ADUW9JQ/Heng 等 - 2024 - A smart mask for exhaled breath condensate harvesting and analysis.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/6X67QLLZ/science.adn6471_movie_s5.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/C39RULDH/science.adn6471_movie_s2.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/D4ADNCJL/Heng 等 - 2024 - A smart mask for exhaled breath condensate harvesting and analysis.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/DXKU547Q/science.adn6471_movie_s7.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/FGRQG52F/science.adn6471_movie_s1.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/FSX3ZZES/science.adn6471_movie_s8.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/H6HPHY5F/science.adn6471_movie_s3.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/QPFYC7IV/science.adn6471_movie_s4.mov;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/YKJNY8XH/science.adn6471_movie_s6.mov}
}

@phdthesis{LiLinZeJiYuLandsatYaoGanYingXiangDeZhiBeiWuHouJianCeJiXiaoChuanZhuYuanFengXianGuanLianXingFenXi2021,
  type = {{博士学位论文}},
  title = {{基于Landsat遥感影像的植被物候监测及哮喘住院风险关联性分析}},
  author = {{李林泽}},
  year = {2021},
  address = {武汉},
  doi = {10.27379/d.cnki.gwhdu.2021.002235},
  abstract = {哮喘病已经成为当今世界上最为普遍的疾病之一。其中,由春季花粉引起的哮喘发病风险占据重要比重。然而,由于花粉浓度监测站点较少、过敏原数据难以获取等问题,使用传统的地表观测手段往往不能满足花粉对哮喘病暴露风险的研究需要。近年来不断有学者尝试利用遥感植被物候信息反映地表植被的生长规律,但较少研究论证长时间序列遥感植被春季物候与春季花粉释放周期(起始时期,周期长度等)的时空相关性,以及在气候环境变化条件下遥感植被物候动态变化与哮喘病住院风险的关联性。本文从导致春季哮喘病住院的主要诱因(花粉)入手,研究分析了利用遥感植被春季物候信息预测花粉释放起始时期及其动态变化的可行性,为大区域尺度哮喘病住院风险评估提供了数据支持。在此基础上,研究了遥感植被春季物候的动态变化对哮喘病住院风险的影响,为环境流行病学研究提供了新的视角。基于遥感影像的植被物候模型日趋成熟。但是,随着对不同植被物候活动的长时间、精细化监测需要,以及植被物候对微生态环境、人类健康影响的研究需求,提高遥感植被物候监测的空间分辨率和模型预测精度越来越重要。此外,由于使用单一的遥感植被物候信息很难全面反映实际的地表植被物候活动规律,综合考虑包含遥感影像在内的多源数据(如温度,降水、光照、土壤含水量等),结合气候学、物候学等不同学科优势,建立多源数据融合模型是提高地表植被物候预测精度的关键。提高遥感植被物候模型的预测精度能为哮喘病住院风险研究提供数据保障,使用高质量的植被物候信息也能够在植被春季物候动态变化与哮喘病住院风险关联性分析中得到更加真实可信的研究成果。本文围绕遥感植被物候监测关键技术以及遥感植被物候与哮喘病住院风险关联性分析,研究分析了遥感植被物候模型在Landsat影像中提取春季物候信息的不确定性及其受到气候环境因素的影响;建立了提高地物光谱信息预测精度的遥感影像时空融合模型;建立了提高了地表植被物候信息预测精度的多源数据融合模型;研究了遥感植被春季物候与哮喘病住院风险关联性分析的理论合理性和技术可行性。本文的主要研究内容和成果归纳如下:1.研究了遥感植被物候模型在Landsat影像中提取植被物候信息的技术流程,深入分析了遥感植被物候模型在提取逐年植被春季物候信息的不确定性。为了消除气候环境变化对不同时间周期Landsat遥感影像提取的植被春季物候信息的影响,本文也研究分析了遥感植被春季物候信息在逐年气候环境变化(温度和降雨量)中的响应。本文选取了北美...},
  collaborator = {{李建松} and Sapkota, Amir},
  langid = {chinese},
  school = {武汉大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HJLWS2KP/基于Landsat遥感影像的植被物候监测及哮喘住院风险关联性分析_李林泽.pdf}
}

@phdthesis{LinBiYiJiYuJiSuanJiShiJueDeHuaFenJianCeSuanFaDeYanJiuYuYingYong2020,
  type = {{硕士学位论文}},
  title = {{基于计算机视觉的花粉检测算法的研究与应用}},
  author = {林必艺},
  year = {2020},
  address = {天津},
  abstract = {气传花粉是常见的过敏原之一,严重危害着特定敏感人群的身体健康,因此,花粉浓度播报业务已经越来越成为气象局不可或缺的业务之一。目前常见的花粉浓度检测方式为首先对空气中的花粉进行收集,制作成花粉涂片,然后在显微镜下寻找并统计花粉的数量,使用统计结果推算空气中花粉浓度。人工识别和统计花粉数量存在效率低,成本高等缺点。本文首先采集显微镜下的花粉图像并构建成数据集,然后基于计算机视觉技术中的数字图像处理技术和深度学习技术,构建花粉检测模型,最后开发并部署花粉检测系统。 本文的主要工作归纳如下: (1)基于显微镜的电子目镜等硬件设备,开发花粉图像采集与标记系统,通过该采集系统对气象局提供的2019年的花粉涂片进行采集。采集完成后对图像中的花粉的位置进行标记和保存,构建成花粉图像数据集。 (2)分析所采集的花粉图像的特征,分别基于数字图像处理技术和深度学习技术,构建两个花粉检测模型。前者主要用到的算法有霍夫圆检测,区域生长,径向展开特征提取等。后者使用的深度目标检测模型为优化的YOLOv3模型。通过两个模型的融合和优化,形成最终使用的花粉检测模型。 (3)以Spring Boot后台框架和Bootstrap前端框架为主体,以检测模型为核心,开发并部署B/S架构的花粉图像在线检测系统。系统通过Redis数据库缓存登录凭据,实现用户单点登录的功能,增强了系统的安全性;通过Nginx反向代理服务器,实现检测服务横向扩展的能力。检测系统通过校园网分配的IPv6公网地址提供检测服务。},
  langid = {chinese},
  school = {天津大学},
  keywords = {,/unread,Spring Boot,YOLOv3},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/7EYGMETW/基于计算机视觉的花粉检测算法的研究与应用_林必艺.pdf}
}

@article{LiuChangHaiJiYuShuJuZengQiangHeTeZhengRongHeDeHuaFenTuXiangFenLei2025,
  title = {{基于数据增强和特征融合的花粉图像分类}},
  author = {刘昌海 and 张恒 and 陆小锋 and 冯予乐 and 吕森林 and 刘书朋},
  year = {2025},
  journal = {工业控制计算机},
  volume = {38},
  number = {1},
  pages = {97--99},
  issn = {1001-182X},
  abstract = {传统花粉分析识别方法依赖于人工经验标注花粉图像，这一过程耗时且耗费人力。近年来，深度学习技术取代传统方法，广泛应用于花粉分析识别研究领域。然而，目前深度学习方法在识别时间、内存占用和准确性等算法模型效能方面仍有待优化。为进一步提高算法模型的效能，采用了数据增强技术，通过旋转、缩放和平移等手段扩展花粉数据集并采用局部二值模式（LBP）网络实现特征融合，提升花粉识别的准确度。实验结果显示，该方法在花粉识别上取得了95.1\%的准确率和96.4\%的精确率，表明数据增强和特征融合技术在提高花粉识别准确性和效率方面的有效性，为花粉分析识别相关研究领域提供技术和方法参考。},
  langid = {chinese},
  lccn = {32-1764/TP},
  keywords = {,/unread,No DOI found},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/P9QYLBNU/基于数据增强和特征融合的花粉图像分类_刘昌海.pdf}
}

@article{millsImagingPollenUsing2024,
  title = {Imaging Pollen Using a Raspberry Pi and {{LED}} with Deep Learning},
  author = {Mills, Ben and Zervas, Michalis N. and {Grant-Jacob}, James A.},
  year = {2024},
  month = dec,
  journal = {Science of the Total Environment},
  volume = {955},
  pages = {177084},
  issn = {0048-9697},
  doi = {10.1016/j.scitotenv.2024.177084},
  urldate = {2025-02-13},
  abstract = {The production of low-cost, small footprint imaging sensor would be invaluable for airborne global monitoring of pollen, which could allow for mitigation of hay fever symptoms. We demonstrate the use of a white light LED (light emitting diode) to illuminate pollen grains and capture their scattering pattern using a Raspberry Pi camera. The scattering patterns are transformed into 20{\texttimes} microscope magnification equivalent images using deep learning. We show the ability to produce images of pollen from plant species previously unseen by the neural network in training. Such a technique could be applied to imaging airborne particulates that contribute to air pollution, and could be used in the field of environmental science, health science and agriculture.},
  lccn = {1},
  keywords = {/unread,AI,Bioaerosols,Imaging,Palynology,Pollen grains,Sensing},
  annotation = {TLDR: The ability to produce images of pollen from plant species previously unseen by the neural network in training is shown, which could be applied to imaging airborne particulates that contribute to air pollution and could be used in the field of environmental science, health science and agriculture.},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/8RCNEA8R/Mills 等 - 2024 - Imaging pollen using a raspberry pi and LED with deep learning.pdf}
}

@article{WangLiHuaFenGuoMinDeYanJiuXianZhuangYuYingDuiCuoShi2025,
  title = {{花粉过敏的研究现状与应对措施}},
  author = {王丽 and 朱丽珍 and 沈效东 and 余泽龙 and 左龙 and 张世杰},
  year = {2025},
  journal = {现代园艺},
  volume = {48},
  number = {1},
  pages = {31--36},
  issn = {1006-4958},
  doi = {10.14051/j.cnki.xdyy.2025.01.064},
  abstract = {城市作为人类主要聚集地，部分植物因释放过敏花粉而诱发的过敏反应，成为威胁城市人口健康的障碍因子。花粉过敏发展成为一个广泛而复杂的研究领域，涉及花粉过敏原的分子特性、携带过敏原的颗粒的性质以及来源分布等多学科知识，了解空气传播花粉过敏原的趋势，对于花粉相关呼吸系统疾病在全球范围内的高流行率和社会经济影响具有重要意义。本研究梳理了国内外花粉过敏研究进展，分析了影响花粉过敏的因素及对人们健康产生的危害，进一步获得花粉过敏程度的评估方式。在此基础上，归纳出主要致敏植物及花粉过敏的流行趋势与花粉过敏的预防和治疗，指出了要估计城市植被和大气花粉浓度对变态反应者的健康危害，关键是建立高效、快速的监测系统和可靠的变态反应风险指标，以及对未来该领域研究方向提出展望，以期为我国城市健康发展提供科学指导，助力``健康中国''发展战略的实施。},
  langid = {chinese},
  lccn = {36-1287/S},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/F45N68U8/花粉过敏的研究现状与应对措施_王丽.pdf}
}

@phdthesis{WangQuanZengJiYuShenDuXueXiDeHuaFenZiDongJianCeSuanFaYanJiu2020,
  type = {{硕士学位论文}},
  title = {{基于深度学习的花粉自动检测算法研究}},
  author = {王全增},
  year = {2020},
  address = {北京},
  abstract = {伴随着城市化进程的发展,花粉过敏人群日益增多。花粉症已经成为季节性流行病。准确及时的花粉预报可以为花粉过敏患者的正常生活提供更好的保障。花粉检测是花粉预报的基础技术,其目的是在采集的花粉样本图片中准确地识别花粉颗粒。目前的花粉检测任务需要依靠有专业经验的研究人员在图片中手工标注花粉颗粒。这种费时费力的方式,无法满足花粉预报的要求。随着深度学习的快速发展,目标检测算法的精度得到很大提高。但复杂的网络结构会带来检测效率的降低。为了满足花粉预报准确性和实时性的要求,花粉检测算法需要在检测精度和检测效率之间取得良好的平衡。针对这一需求,开展了基于深度学习的花粉自动检测算法研究。具体内容如下:(1)构建了花粉自动检测数据集。由于缺少公开的花粉检测数据集,所以针对北京市两种主流的花粉采集方式构建了花粉检测数据集。该数据集包含了扫描电子显微镜下的花粉图片和光学显微镜下的花粉图片。通过对数据集的统计和测试,验证了该数据集符合自然环境下的花粉分布规律,可以较好的完成花粉检测算法的测试任务。(2)构建了基于特征融合的快速花粉检测算法。针对花粉数据集中小目标花粉比例较大的特点,通过特征融合的方式集合浅层特征和深层特征用于花粉检测。与此同时,本算法使用了一种结构简单的基础主干网络用于特征提取,以保证较小的模型规模和较高的检测效率。(3)构建了基于自注意力机制的花粉检测算法。针对花粉颗粒容易破裂畸形,易与背景融合的特点,添加了空间自注意力模块和通道自注意力模块。两个并联的自注意力模块可以捕捉图片的上下文信息,突破局部感受野的限制,提高花粉检测精度。本文首先针对缺少适用数据集的问题建立了符合自然分布规律的花粉检测数据集。然后使用深度学习的方法设计了一种快速花粉检测算法,在检测精度和检测效率之间取得了较好的平衡。在深入分析花粉数据集特点的基础上,本文针对性地使用特征融合和自注意力机制的方法,提高了花粉检测精度,并在构建的花粉数据集上验证了算法的有效性。},
  langid = {chinese},
  school = {北京工业大学},
  keywords = {,/unread},
  annotation = {citation: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/6AV4NS3S/基于深度学习的花粉自动检测算法研究_王全增.pdf}
}

@phdthesis{WangYaQunJiYuShuJuZengGuangHeTeZhengRongHeDeHuaFenXiBaoTuXiangShiBie2020,
  type = {{硕士学位论文}},
  title = {{基于数据增广和特征融合的花粉细胞图像识别}},
  author = {王亚群},
  year = {2020},
  address = {北京},
  abstract = {花粉细胞图像识别在空气检测、化石鉴定、蜂蜜质量控制、植物年代测定与跟踪等环境和医学领域扮演着重要角色。传统花粉细胞图像识别基于人工进行特征设计,并使用机器学习方法进行分类识别,不仅对从业人员的植物形态学知识与实践经验要求较高,而且通常需要复杂的特征工程。而基于卷积神经网络的花粉细胞图像识别能够避免复杂操作产生不可控的精度误差,实现花粉图像高效、可靠的分类识别。为此,本文开展基于卷积神经网络的花粉细胞图像识别研究,主要工作和贡献总结如下:(1)针对花粉细胞图像采集、成像困难造成的数据集规模较小问题,设计了一种基于数据增广的花粉细胞图像识别方法。在POLEN23E花粉细胞图像数据的基础上,加入三种分割花粉细胞图像数据集,并对POLEN23E数据集进行基于Mixup算法的统计学增广,构建了 MixupPOLEN23E+CPOELN23E等多个增广花粉细胞图像数据集。实验表明,MixupPOLEN23E+CPOELN23E增广图像集取得了较好识别效果,本文的数据增广方法将花粉细胞图像识别准确率平均提升3.4\%-4.6\%,有效弥补了样本量不足的缺陷。(2)针对花粉细胞图像识别困难问题,设计了一种基于特征融合的花粉细胞图像识别方法。一方面对花粉图像进行传统特征提取,构建基于词频的视觉词典,并与深度残差网络特征图进行级联融合,加强样本的特征学习。另一方面在网络中引入了 Focal Loss损失函数,减小了简单/困难样本识别差异。实验表明,基于特征融合的识别方法能有效改善花粉细胞图像特征提取质量,提高花粉细胞图像识别精度。},
  langid = {chinese},
  school = {北京邮电大学},
  keywords = {,/unread},
  annotation = {citation: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/XTGPS88R/基于数据增广和特征融合的花粉细胞图像识别_王亚群.pdf}
}

@article{XiaoYuHuiChengShiZhiMinHuaFenJianCeJiDuiRenTiJianKangDeYingXiangYanJiuJinZhan,
  title = {{城市致敏花粉监测及对人体健康的影响研究进展}},
  author = {肖雨慧 and 韩立建 and 李伟峰 and 周伟奇 and 钱雨果 and 刘佳欣 and 朱孟郡},
  journal = {生态学杂志},
  pages = {1--15},
  issn = {1000-4890},
  doi = {10.13292/j.1000-4890.202510.050},
  abstract = {城市生态建设过程中对高致敏植物和致敏花粉问题的系统认知与管理不足，盲目引种或将导致城市致敏花粉问题日益严重。为了系统理解致敏花粉从致敏花粉植物到致敏花粉的影响过程，本文基于``格局-过程-效应''的多维视角综述了致敏花粉的源头及监测方法、大气中的运移特征，及其潜在的综合影响和风险评估方法。首先，梳理了气传致敏花粉的主要来源植物，总结了致敏花粉的监测技术；其次，围绕植物开花的生长发育阶段与环境因子的关系，总结了基于遥感和深度学习等技术的致敏植物花期识别方法；第三，概括了致敏花粉浓度的预测方法，辨析了盛花期和非盛花期两种截然不同的致敏花粉影响与运移模式，总结了致敏花粉的综合风险评估方法。最后，文章指出了当前在城市致敏花粉预测、运移模拟以及健康评估等方面存在的不足，并展望了未来研究方向和潜在的应用前景。本文对于提升城市花粉过敏防控水平、促进城市生态建设具有重要的参考意义。},
  langid = {chinese},
  lccn = {21-1148/Q},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/UTE9QI4R/城市致敏花粉监测及对人体健康的影响研究进展_肖雨慧 (1).pdf}
}

@phdthesis{YangJiaJingJiYuFuJiXianWeiChengXiangDeShiShiHuaFenJianCeXiTongDeYanJiu2022,
  type = {{博士学位论文}},
  title = {{基于富集-显微成像的实时花粉监测系统的研究}},
  author = {杨家婧},
  year = {2022},
  address = {杭州},
  abstract = {近年来,随着花粉过敏人群的增加,人们对于知晓大气中花粉种类与浓度的需求正在逐渐上升。传统的花粉颗粒监测技术依赖长时间的采样和人工目视检测,整套流程耗时且需要专业技术人员参与,难以建立大范围的监测点,普及程度受限。颗粒物光学自动监测技术可通过单颗粒光学信号检测进行实时分析,然而该技术难以结合富集采样技术,对于低浓度的花粉颗粒检测准确度不高。基于静电、气流曳力等原理的捕集技术,结合花粉显微成像技术以及人工智能图像鉴别算法可实现高捕集率、高准确度、高自动化的低浓度花粉实时监测,具有应用于花粉大规模监测的潜力。第一章依据花粉富集和检测方法的不同,对花粉气溶胶监测领域的现状、发展与常见的商业化仪器进行了分类综述。对于先富集再检测的花粉分析技术,详细介绍了花粉被动与主动的富集采样方法、手动和自动检测方法,并分析了每种技术的优劣;对于流式花粉检测技术,主要介绍了利用单颗粒花粉的散射光和激光诱导荧光法实现花粉的鉴别和计数,以实现实时检测。最后,介绍了目前发展较为成熟的基于光学及成像原理的花粉自动识别系统。第二章发展了一种基于静电捕集和显微成像的全集成化花粉实时监测仪。该监测仪集成了无损进样模块、静电捕集模块、自动显微成像模块和自清洁模块,实现了花粉颗粒的定量引入、连续捕集、自动成像、机器视觉图像分析、捕集区域自清洁和循环使用。在三个数量级的大范围花粉浓度内,系统捕集效率均稳定25\%左右。此外,我们还提出了用于生成标准浓度的含花粉气体的方法,将花粉无损地传输到捕集模块,以便于仪器性能的定量化评估。与传统的花粉分析仪器相比,本监测仪实现了从捕集、成像到分析全过程的自动化,可实时获取花粉浓度,大大节省人工,提高了分析效率。第三章研制了基于静电-曳力捕集和多通道图像鉴别的自动花粉监测系统。基于空气倍增原理,构建了独立的花粉传输模块,实现了花粉颗粒的无损传输。在静电捕集的基础上增加了气流曳力捕集,将花粉颗粒的捕集效率提升至63\%,实现了高采样流量下的高效捕集;设计构建了多通道显微成像模块,可拍摄明场和两个不同波段的荧光通道花粉图片;通过荧光强度比值初步实现了花粉种类的鉴别,同时发展了基于深度学习的Alex Net图像分析算法,区分七种花粉颗粒的准确率达97\%以上。本监测仪实现了空气中花粉的无损传输、高效捕集、快速多通道显微成像以及人工智能图像分析的全流程自动化,可快速获取空气花粉信息,为构建大范围的花粉监测网络提供了设备与技术支撑。},
  langid = {chinese},
  school = {浙江大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BZW66V4V/基于富集-显微成像的实时花粉监测系统的研究_杨家婧.pdf}
}

@phdthesis{YaoJiangShangHaiHuXiDaoGuoMinRenQunShiKongFenBuBiaoZhengJiQuDongYinSuFenXi2024,
  type = {{硕士学位论文}},
  title = {{上海呼吸道过敏人群时空分布表征及驱动因素分析}},
  author = {姚江},
  year = {2024},
  address = {上海},
  abstract = {花粉过敏因其广泛的影响和严重的危害被世界卫生组织被列为21世纪重点研究和防治的三大疾病之一,引起了全球范围内的广泛关注。据统计,世界上大约30\%的人口都会受到大气中过敏性花粉的影响,其中约5亿人更是深受过敏性鼻炎(AR)的困扰。这不仅严重影响了人们的生活质量,还对经济财产和生命安全造成了威胁。因此,深入探索呼吸道过敏人数与花粉浓度之间的相关性,对于预防和治疗花粉过敏具有重要意义。 上海因其城市人口密集、致敏植被分布广泛的原因成为中国儿童过敏性疾病患病率最高的城市,本文以上海市为研究区对上海市2011-2020年过敏人群的时空分布特征及驱动因素进行分析。这里开展相关研究,有望为预防和治疗花粉过敏提供更为科学的依据和有效的方法。 本研究采用氯雷他定这一抗过敏药物来表征呼吸道过敏人数,同时以悬铃木的数量来表征花粉浓度。本文运用统计学方法对数据进行了初步分析,了解了氯雷他定销售量和悬铃木数量的基本分布和变化趋势。以相关性分析和地理加权回归分析方法探究氯雷他定销售量和悬铃木数量之间的相关性及其影响因素。本文的主要结论如下: (1)从时间维度来看,上海市2011-2020年10年间呼吸道过敏高发时期是5月,6-7月和9月,分别占全年过敏人数8.9\%、9.1\%、9\%、和9.2\%。且过敏期随着季节出现波动,夏季的过敏人数比春季多6.56\%,秋季比夏季多1.41\%,冬季人数相对春夏秋三季有所减少,但仍占过敏人群总数的22.6\%。2011年-2014年过敏人数从42万增加到66万,增长率高达57.45\%,2014年至2017年过敏人数大幅减少,整体数量减少了47.06\%。从空间维度来看,城市中心是呼吸道过敏易发区,城市中心周边其他地区是少发区。如呼吸道过敏人群杨浦区、徐汇、黄浦区、闵行区、浦东新区等中心地区共占74\%。而长宁区、嘉定区等地分布较少,长宁只占1\%。 (2)悬铃木在空间上的分布呈现出在徐汇区、普陀区、杨浦区、黄浦区、浦东新区等中心地区的集聚分布特征,占上海市悬铃木总数的58.5\%,而金山区、奉贤区、青浦区和嘉定区等地区只占6\%,说明在郊区及其他地区悬铃木随机分布或分散分布。氯雷他定销售量和悬铃木数量两者之间存在一定的正相关关系,即悬铃木数量的增加可能会导致氯雷他定销售量的上升,从而间接反映出呼吸道过敏人数的增加。 (3)通过构建地理加权回归模型,我们发现气温这一驱动因素呈现空间异质性,对过敏人群确实存在一定的正...},
  langid = {chinese},
  school = {上海师范大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/4ZKUWIV2/上海呼吸道过敏人群时空分布表征及驱动因素分析_姚江.pdf}
}

@phdthesis{ZhangManLinChengShiHuaFenZhiMinZhiWuGouChengFenBuJiQianZaiWeiHaiDuiBiYanJiu2022,
  type = {{硕士学位论文}},
  title = {{城市花粉致敏植物构成、分布及潜在危害对比研究}},
  author = {张曼琳},
  year = {2022},
  address = {重庆},
  abstract = {气传致敏花粉会引发过敏症,且可通过每年在花粉季的反复接触逐渐加重,危害人体健康,严重时甚至危害生命。随着城市化的快速发展,为了满足人们对绿地的需求,大量花粉致敏植物在城市中被广泛引种栽培,导致城市花粉症患者数量激增。 本研究以深圳市和重庆市为案例,基于建成区各144个样地的实地调查数据,分析花粉致敏植物的种类构成与时空分布,揭示不同城市花粉致敏植物的种类构成特点及其时空分布格局差异,并探寻其共性与规律,通过构建花粉浓度及花粉致敏危害潜力计算公式,预测城市中花粉致敏植物的潜在危害性。研究结果显示: (1)调查记录到深圳市花粉致敏植物47科89属114种,其中外来种占46.50\%。重庆市植物调查共记录花粉致敏植物54科104属130种,其中外来种占40.76\%。均以来自美洲的植物种居多,分别占47.22\%和46.43\%。两座城市花粉致敏植物均以开花观赏植物居多,占比均超过50.00\%。重庆花粉致敏植物种数以居住区绿地最多,乔灌草分别有45种、46种和52种。 (2)深圳市和重庆市花粉致敏植物盛花期均出现在春夏季,峰值分别出现在7月和5月,各占总种数的50.00\%和50.78\%。 (3)深圳市花粉致敏植物中,花粉致敏等级为I级的物种达97种,II级有15种,III级仅2种,即垂柳(Salix babylonica)和旱柳(Salix matsudana)。重庆市主城区花粉致敏植物中,花粉致敏等级为I级的物种达96种,II级有33种,III级仅2种,即垂柳和葎草(Humulus scandens)。两座城市均以I级轻度致敏等级花粉致敏植物为主。 (4)重庆花粉浓度潜力在4月份达到峰值,占全年总值62.38\%,深圳市花粉浓度潜力出现在5月,占全年总值的63.68\%。垂柳在深圳市和重庆市都位于乔木花粉浓度潜力前十,小蜡和苏铁在深圳市和重庆市都位于灌木花粉浓度潜力前十,马唐、狗牙根和车前均位于两个城市草本花粉浓度潜力的前十,且乔木和草本的花粉浓度潜力均高于灌木。 (5)重庆市二级重度危害样地仅出现在大渡口区春季花粉致敏样地,春季各行政区一级重度危害样地比其他季节数量更多,重庆市除冬季外各季节均有二级中度危害样地分布,但数量较少,全年均有一级中度和轻度危害等级样地分布。深圳市全年无二级重度危害样地出现,除冬季外各行政区均有一级重度危害样地分布,深圳市各行政区均分布有一级中度和轻度危害等级样地,但均无二级中度危害样地。重庆市和深圳...},
  langid = {chinese},
  school = {西南大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/2QNVGNMX/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳 (1).pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/LNYGXJ9I/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/QQQRBHG5/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳.pdf}
}

@article{ZhangManLinChengShiHuaFenZhiMinZhiWuZhongLeiGouChengFenBuYuQianZaiWeiHaiPingGuYiShenZhenShiWeiLi2021,
  title = {{城市花粉致敏植物种类构成、分布与潜在危害评估------以深圳市为例}},
  author = {张曼琳 and 潘妮 and 赵娟娟 and 李明娟 and 江南},
  year = {2021},
  journal = {生态学报},
  volume = {41},
  number = {22},
  pages = {8746--8757},
  issn = {1000-0933},
  abstract = {气传致敏花粉会引发过敏症,且可通过每年在花粉季的反复接触逐渐加重,危害人体健康,严重时甚至危害生命。花粉致敏植物在城市中被广泛引种栽培,导致城市花粉症患者数量激增。以深圳市为案例,基于建成区600个样地的实地调查数据,分析花粉致敏植物的种类构成与时空分布,通过构建花粉浓度及花粉致敏危害潜力计算公式,评估花粉致敏危害潜力及其分布特点。结果显示:(1)调查记录到深圳市建成区花粉致敏植物46科92属186种,其中外来种占43.37\%,其中美洲、亚洲和大洋洲来源占国外外来种的81.00\%。花粉致敏植物种数以公园绿地最多,达126种。植物花粉致敏等级以I级为主,达154种。(2)花粉致敏植物的盛花期为春夏季,占全年累计开花种数的65.02\%。最高峰出现在8月,开花种数达92种。(3)花粉浓度潜力最高值也出现在8月,占全年总值的12.13\%。豆科和禾本科植物贡献比例较大,分别占乔木和草本总值的40.86\%和64.13\%。龙岗区花粉浓度潜力占比较高,占各季花粉量的26.06\%---29.42\%。(4)冬春两季各样地花粉致敏危害等级均不高,但夏秋季有些样地达到二级重度危害。罗湖区调查样地全年花粉致敏危害等级均较低,光明区在春夏秋季致敏危害等级皆较高。高危害等级样地主要出现在附属绿地及公园绿地。(5)花粉致敏植物防控措施情景模拟结果,去除主要花粉致敏植物种类比降低所有花粉致敏植物的花粉浓度潜力更有效。本研究为城市花粉致敏植物的管理、规划、养护和研究提供参考。},
  langid = {chinese},
  lccn = {11-2031/Q},
  keywords = {,/unread,No DOI found},
  annotation = {citation: 26},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/4W8DYGHN/张曼琳 等 - 2021 - 城市花粉致敏植物种类构成、分布与潜在危害评估——以深圳市为例.pdf}
}

@phdthesis{ZhangXinYueJiYuTuXiangChuLiDeHuaFenShenDuTuXiangXiuZhengFangFaYanJiu2023,
  type = {{硕士学位论文}},
  title = {{基于图像处理的花粉深度图像修正方法研究}},
  author = {张心月},
  year = {2023},
  address = {呼和浩特},
  abstract = {花粉过敏症(即花粉症)在全球范围内均是常见病和多发病,并且花粉症的患病率近年呈现明显的上升趋势。花粉症会有鼻痒、流鼻涕、打喷嚏、眼痒等症状,甚至可以引起胸闷、憋气、哮喘等下呼吸道症状。现有的调查资料显示我国各城市和地区的花粉症患病率介于0.9\%到5\%之间,即使按照现有资料的最低发病率计算,我国花粉症的患者数量也大至千万以上。内蒙地区长有较多艾蒿、蒲公英等植物使得过敏季节飘散较多花粉,至花粉症患者呈逐年上升趋势。通过花粉三维形状重建了解花粉形状特征有助于研究人员对花粉症开展针对性的研究,同时对于花粉知识的科普用积极推动作用。到目前为止,研究者对深度图像修正进行了大量研究,但这些方法都不适用于花粉图像深度图估计。这些方法都是针对现实物体或场景的三维重现为目标,并且有专门的深度信息获取设备为辅助。但花粉图像是由花粉的电子反射信号生成的,不具备距离信息;同时,花粉样本个数较少,无法用基于深度学习的方法进行估计。本文以花粉三维形状重建为目标,研究基于图像处理的花粉图像深度信息修正方法。提出的基于图像处理的花粉图像深度信息修正算法,旨在保护花粉图像原本形状信息的基础上对其进行深度信息修正。首先,对花粉图像进行预处理并提出了一种花粉图像最长线提取方法。为避免噪声对结果图像的影响,对图像进行去噪处理接着获取花粉边缘信息;对边缘图像细化提取骨架边缘;最后计算骨架边缘的最长线轮廓信息。其次,提出了一种基于tanh函数的花粉深度图像修正方法。利用花粉图像多数是椭圆立体结构的特点对花粉深度图像进行修正。研究中假设花粉图像的中心点到边缘的灰度分布服从tanh函数的分布。修正原则为花粉图像中心位置距离观察点最近,因此对其中心位置进行大幅度灰度值修正。花粉边缘处距观察点最远,因此进行小幅度灰度值修正。本研究不考虑背景的深度信息,因此将图像背景置为黑色。根据以上原则使结果图像灰度值由花粉中心处向边缘递减,使图像在视觉上具备深度信息。接着,提出了两种基于Gamma函数的花粉深度图修正方法。第一个方法中,我们将基于tanh函数的花粉深度图像修正方法中的假设的服从tanh函数分布修改为服从Gamma函数的分布,以此为基础在原始图像中附加服从Gamma函数的灰度信息。使修正后图像的中心点变亮,边缘点基本保持不变。但通过第一个方法修正后的图像的花粉边缘部分还是比较亮,这不利于对花粉的三维形状重建。因此,我们对基于Gamma函数的花粉深度图修正方法一进行了...},
  langid = {chinese},
  school = {内蒙古工业大学},
  keywords = {,/unread,Gamma,tanh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HAMU5AMM/基于图像处理的花粉深度图像修正方法研究_张心月.pdf}
}

@article{ZhouWeiQiChengShiLuDiHuaFenZhiMinYanJiuJinZhan2024,
  title = {{城市绿地花粉致敏研究进展}},
  author = {周伟奇 and 邓文萱 and 秦海明},
  year = {2024},
  journal = {生态学报},
  volume = {44},
  number = {23},
  pages = {10936--10952},
  issn = {1000-0933},
  doi = {10.20103/j.stxb.202401110097},
  abstract = {城市绿地中部分植物产生的气传花粉具有致敏性，可引发城市居民的花粉过敏症，因而城市绿地成为城市花粉致敏问题的主要来源和研究区域。随着城市化进程的推进，花粉过敏症越来越普遍，且存在长期致敏隐患，成为一个广受关注的城市居民健康问题。基于20世纪以来的花粉致敏研究热点和发展趋势，综述了城市绿地致敏花粉的监测、花粉致敏性的评价方法、花粉致敏的影响因素，以及花粉致敏风险预测。针对花粉致敏现象的成因、地区差异、危害与风险等分析评价不明朗，以及研究主题之间联系性不强等问题，系统归纳了致敏花粉的采集方法、种类与时空分布规律，阐明了评价花粉致敏性的各类方法与适用性条件，分析了影响花粉致敏的城市生态因子，提出了花粉致敏风险预警与防范措施，并指出了未来的发展方向。可为缓解花粉致敏问题提供研判基础、为防控致敏风险提供优化方案、为建立花粉致敏研究体系提供科学依据，进而降低花粉致敏对城市宜居性带来的不良影响。},
  langid = {chinese},
  lccn = {11-2031/Q},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BKHNLWVS/城市绿地花粉致敏研究进展_周伟奇.pdf}
}

@phdthesis{ZhuYanGangJiYuDuoTeZhengRongHeDeHuaFenTuXiangShiBieSuanFaYanJiu2018,
  type = {{硕士学位论文}},
  title = {{基于多特征融合的花粉图像识别算法研究}},
  author = {朱延刚},
  year = {2018},
  address = {南京},
  abstract = {在生物医学领域,花粉图像的识别分类不但具有重要的研究意义,而且市场应用前景广阔。传统的花粉分类工作主要借助人工在显微镜下完成,不仅对操作者的相关知识经验具有一定要求,而且分类过程缓慢,分类准确性较低。考虑到花粉图像有着和普通图像类似的轮廓纹理特征,随着近几年图像处理技术理论的迅速发展,通过提取花粉特征,完成对花粉图像识别分类成为一种有效的解决方法。由于原始花粉图像在采集的过程中受到光照、噪声污染等外部因素干扰,花粉图像的质量受到了不同程度的影响,导致识别率较低,因此要求提取花粉的特征具有较强的鲁棒性,提取的过程具有一定的实时性,最终可以得到较为理想的识别效果。本文就花粉图像的多特征提取融合进行研究,并从特征描述子和分类识别算法两个方面着手,既增强了算法的分类识别的准确度,也提高了算法的运行效率和实时性。主要研究的内容包括:(1)针对传统的特征提取算法往往只利用了单个花粉图像特征,提取的识别特征普遍存在抗噪能力不强、不具有不变性等缺点,本文运用传统机器学习的相关图像识别理论,提出了一种融合Zernike矩与BoF-SURF特征融合的花粉图像分类识别算法。首先对花粉图像的Zernike矩特征进行提取,然后提取改进后的SURF特征描述子,再对SURF特征进行K-Means聚类,构建加速鲁棒性特征包BoF-SURF,最后对这两种特征进行融合,由支持向量机SVM完成识别分类。由于Zernike矩和SURF描述子都具有不变性,针对花粉的尺度和旋转变化具有良好的鲁棒性,正确识别率也较高。(2)针对传统的花粉图像分类算法在提取特征的过程中,如何选取最有效的提取特征具有一定的复杂性,本文运用深度机器学习图像识别框架,提出了一种基于卷积神经网络CNN的特征融合花粉图像分类识别算法。本算法首先对花粉图像进行标准化处理,作为第一个训练网络的输入层图像数据矩阵,然后再对花粉图像进行方向梯度直方图HOG特征化处理,作为第二个训练网络的输入层图像数据矩阵,处理层结构为双层卷积-池化层,并且对各训练网络层的卷积矩阵参数进行了优化改进,以针对不同的数据矩阵进行更有效的特征抽取,然后输入到本算法增加的特征融合层,再经过全连接层的处理整合,最后通过输出层的softmax和损失函数完成对模型的训练,最终用于对花粉图像的分类。},
  langid = {chinese},
  school = {南京信息工程大学},
  keywords = {,/unread,BoF-SURF,HOG,Zernike},
  annotation = {citation: 5},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HSNCQJ9Z/基于多特征融合的花粉图像识别算法研究_朱延刚.pdf}
}

// ================================================================
// 第二章专用引用文献 (从 chapter2.bib 合并)
// ================================================================

@misc{GuoJiaJiKongJuGongGongChangSuoWeiShengJianYanFangFaDi3BuFenKongQiWeiShengWu2013,
  title = {{公共场所卫生检验方法 第3部分：空气微生物}},
  author = {{国家疾控局}},
  year = {2013},
  month = dec,
  number = {GB/T 18204},
  urldate = {2025-08-25},
  langid = {chinese},
  keywords = {/unread},
  annotation = {CCS: C51\\
ICS: 13.060\\
applyDate: 2014-12-01},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/XJICDGRQ/newGbInfo.html}
}
