@phdthesis{BaoXiaoDongJiYuGuangXianGongJuJiaoLaManGuangPuJiShuDeDuoChangJingZhiNengJianCeFangFaYanJiu2025,
  type = {{博士学位论文}},
  title = {{基于光纤共聚焦拉曼光谱技术的多场景智能检测方法研究}},
  author = {{包晓栋}},
  year = {2025},
  address = {北京},
  doi = {10.27522/d.cnki.gkcgs.2025.000016},
  abstract = {拉曼光谱是一种能提供分子振动信息的光谱技术,凭借其非侵入、快速、无损的特点,能准确地提供分子的指纹信息,以实现对物质的识别和鉴定,目前已被应用到生物医学、材料科学、工业检测、地球科学等广泛的研究领域。拉曼光谱仪作为检测拉曼光谱的重要工具,目前主要有共聚焦式和光纤式两种。但由于各个领域检测场景的不同,单一制式的光谱仪往往无法满足检测的多样性需求,而导致研发和采购成本的提高。同时,复杂的应用场景也给传统的光谱分析方法和识别算法带来了新的挑战。针对这一系列问题,本文提出``硬件基座+应用场景+智能算法''的协同研究方法,通过一套高检测自由度的拉曼光谱检测系统,结合人工智能算法,解决多场景下的检测问题。文本的主要研究内容如下:(1)针对拉曼检测模式多样性的需求,提出将共聚焦式和光纤式拉曼光谱仪的特点结合,通过在光路中引入反谐振空芯光纤,将共聚焦拉曼光谱仪的检测端与光谱仪主机解耦,实现了``单主机+自由检测端''的硬件模式,提高了检测端的空间自由度。反谐振空芯光纤实现了单光纤传输激发光和拉曼信号,且低拉曼背景特性使其在使用过程中无需任何尖端处理。与传统光纤拉曼不同,本系统可以搭配现有的高性能显微物镜使用,实现了显微检测模式和探头检测模式的自由切换,且均具备共聚焦检测能力,能满足各种研究场景的多样化智能检测需求。(2)面向跨仪器生物类拉曼光谱检测的场景,针对拉曼光谱仪的台间差异导致分类效果差的问题,使用更换不同反谐振空芯光纤的方法模拟了采集过程,通过数据降维可视化和传统分类算法的跨数据集测试证明了台间差异的存在,并提出一种基于孪生网络的小样本对比学习算法,利用模块化设计思路,实现不同光谱编码器的自由插拔,在训练集数据绝对数量较少的情况下,对比了Res Net、Transformer和LSTM三种不同编码器的分类效果,取得了远超传统光谱对比算法的准确率,实现了跨仪器生物类拉曼光谱的分类。(3)面向生物制药工业的颗粒物检测场景,针对开放空间的未知类别颗粒物拉曼光谱无法识别的问题,提出一种基于Open Max的开集识别及动态类增量的深度学习方法,通过已知分类数据的激活向量拟合Weibull分布,并重新构建带未知分类的Open Max函数预测分类概率,实现了对未知颗粒物拉曼光谱的高灵敏性识别。并提出使用多种策略的模型微调方法进行类增量学习,在大规模参数和小规模参数的微调任务中均取得了较高的分类准确率,实现了工业场景中模型...},
  collaborator = {{李备}},
  langid = {chinese},
  school = {中国科学院大学（中国科学院长春光学精密机械与物理研究所）},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/ALP9GNK6/基于光纤共聚焦拉曼光谱技术的多场景智能检测方法研究_包晓栋.pdf}
}

@phdthesis{CaoYuanXinGaiJinHuaShouFenSuanFaJiQiZaiTuXiangFenGeZhongDeYingYongYanJiu2024,
  type = {{硕士学位论文}},
  title = {{改进花授粉算法及其在图像分割中的应用研究}},
  author = {曹渊鑫},
  year = {2024},
  address = {西安},
  abstract = {花授粉算法(Flower Pollination Algorithm,FPA)是一种简单高效的元启发式算法。其有效模拟了显花植物的授粉行为,并且具有设置参数少、鲁棒性强、结构简单、寻优效率高等优点,但FPA仍存在迭代后期收敛速度慢、脱离局部最优能力较弱等不足。因此,本研究提出两种改进的FPA,并将改进后的FPA应用于图像分割。具体工作如下: (1)针对合成孔径雷达(Synthetic Aperture Radar,SAR)图像中相干斑噪声导致SAR图像的分割难度增加的问题,提出一种基于改进花授粉算法(Improved FPA with Sobol sequence initital strategy and differential evolution mutation strategy,SDEFPA)的 SAR 图像阈值分割方法。通过搜索域变换策略以提高算法搜索效率,采用Sobol序列初始化种群策略提高种群均匀性及多样性,引入DE/current-to-best/1变异策略弥补FPA可能陷入局部最优的不足。CEC 2017测试函数集上的结果表明SDEFPA相较于对比算法拥有更好的求解精度和更快的收敛速度。将二维灰熵作为SDEFPA的适应度函数,分割阈值可视为灰数,利用SDEFPA完成灰数的白化过程,得出最佳分割阈值。图像分割实验结果表明,相比于多策略融合鲸鱼优化算法的二维最大熵法、基于改进自适应差分演化算法的二维Otsu法,所提分割方法分割速度更快、所分割图像质量更高。 (2)针对K-means算法对初始聚类中心敏感,其处理图像分割等较复杂问题时易陷入局部最优导致难以获得满意分割结果的问题,提出一种将改进花授粉算法(Enhanced FPA with the new local search strategy and the adaptive cauchy-gauss mutation strategy,NMFPA)与K-means算法相结合的图像分割方法。首先通过新局部搜索策略引导花粉配子个体进化方向并增加种群搜索范围有效提升了算法的收敛速度及搜索精度;通过自适应柯西-高斯变异策略扩大了种群搜索范围,降低了算法陷入局部最优解的概率。CEC 2017和CEC 2021测试函数集上的结果验证了改进花授粉方法寻优精度高、稳定性好。最后将NMFPA用于优化K-means算法,通过与FPA与K-means算法相结合...},
  langid = {chinese},
  school = {西安理工大学},
  keywords = {,/unread,DE/current-to-best/1,K-means},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/W4BWQSHP/改进花授粉算法及其在图像分割中的应用研究_曹渊鑫.pdf}
}

@article{eryilmazPaperBasedMultiplexedSerological2024,
  title = {A {{Paper-Based Multiplexed Serological Test}} to {{Monitor Immunity}} against {{SARS-COV-2 Using Machine Learning}}},
  author = {Eryilmaz, Merve and Goncharov, Artem and Han, Gyeo-Re and Joung, Hyou-Arm and Ballard, Zachary S. and Ghosh, Rajesh and Zhang, Yijie and Di Carlo, Dino and Ozcan, Aydogan},
  year = {2024},
  month = jul,
  journal = {ACS Nano},
  volume = {18},
  number = {26},
  pages = {16819--16831},
  issn = {1936-0851, 1936-086X},
  doi = {10.1021/acsnano.4c02434},
  urldate = {2025-03-10},
  abstract = {The rapid spread of SARS-CoV-2 caused the COVID-19 pandemic and accelerated vaccine development to prevent the spread of the virus and control the disease. Given the sustained high infectivity and evolution of SARS-CoV-2, there is an ongoing interest in developing COVID-19 serology tests to monitor population-level immunity. To address this critical need, we designed a paper-based multiplexed vertical flow assay (xVFA) using five structural proteins of SARS-CoV-2, detecting IgG and IgM antibodies to monitor changes in COVID-19 immunity levels. Our platform not only tracked longitudinal immunity levels but also categorized COVID-19 immunity into three groups: protected, unprotected, and infected, based on the levels of IgG and IgM antibodies. We operated two xVFAs in parallel to detect IgG and IgM antibodies using a total of 40 {$\mu$}L of human serum sample in {$<$}20 min per test. After the assay, images of the paper-based sensor panel were captured using a mobile phone-based custom-designed optical reader and then processed by a neural network-based serodiagnostic algorithm. The serodiagnostic algorithm was trained with 120 measurements/tests and 30 serum samples from 7 randomly selected individuals and was blindly tested with 31 serum samples from 8 different individuals, collected before vaccination as well as after vaccination or infection, achieving an accuracy of 89.5\%. The competitive performance of the xVFA, along with its portability, cost-effectiveness, and rapid operation, makes it a promising computational point-of-care (POC) serology test for monitoring COVID-19 immunity, aiding in timely decisions on the administration of booster vaccines and general public health policies to protect vulnerable populations.},
  langid = {english},
  lccn = {1},
  keywords = {/unread},
  annotation = {JCR分区: Q1\\
中科院分区升级版: 材料科学1区\\
中科院分区基础版: 工程技术1区\\
影响因子: 16.0\\
5年影响因子: 16.4\\
EI: 是},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/JRRSM2Y6/Eryilmaz 等 - 2024 - A Paper-Based Multiplexed Serological Test to Monitor Immunity against SARS-COV-2 Using Machine Lear.pdf}
}

@phdthesis{FanYuLiZhuQuHuWaiKongJianShouWaiYuanXingKeLiWuYingXiangJiTiShengGaiShanYanJiu2023,
  type = {{博士学位论文}},
  title = {{住区户外空间受外源性颗粒物影响及提升改善研究}},
  author = {{范域立}},
  year = {2023},
  journal = {工程科技I辑},
  address = {武汉},
  doi = {10.27379/d.cnki.gwhdu.2022.001054},
  urldate = {2025-08-22},
  abstract = {近年来,我国空气质量得到明显改善,但外源性颗粒物污染仍然无法得到根本性解决,不利气象条件和区域污染物排放复合作用下的重污染天气过程时有发生,并对居民健康造成严重负面影响。同时,随着我国大气污染治理进入攻坚期,进一步减排潜力也越来越小。因此,扩展颗粒物污染的治理路径、加强面向公众和作用地区的末端治理迫在眉睫。城市住区的设计优化与更新改造,既能够调控直接影响颗粒物扩散、分布的空间要素,也能够通过路网、游园等引导居民活动,进而减少外来大气颗粒物与居民的时空重叠,因而是空间规划介入颗粒物污染治理的重要潜在抓手。现有针对住区环境进行优化改善的研究往往会调整描述住区空间要素和背景环境的多个指标来构建不同场景,再通过仿真模拟、缩比实验来考察各类场景下住区周边相关物理场的状况,并据此提出改善建议。然而,建成环境近地表风场的复杂性和住区建筑群空间形态的高度多样性,使得这一思路可能面临工程可行性和实践可用性的双重困境:对于前者,描述外源性颗粒物污染过程和住区空间形态的常用指标构成了高维度的变量空间,致使可能存在的场景数量远超能够调动的算力和时间资源,带来``维度灾难''问题;对于后者,利用简单指标构造假设性的污染过程和设计方案会与受诸多因素约束的复杂现实情况产生较大偏差,最终导致大量仿真模拟场景再现实世界中不可能存在。针对上述问题,本研究提出一种基于少量的典型颗粒物污染场景和常见住区空间形态模式的住区提升改善路径。具体:1)识别特定区域内典型的外源性颗粒物污染过程模式。首先,建立近地表分钟级颗粒物和气象要素监测网络,得到多站点上颗粒物浓度分钟级时序数据;随后,集成离散小波分解、动态时间规整等信号处理技术方法,克服近地表复杂风场和多来源颗粒物的干扰,提取背景性颗粒物浓度变化规律,从而表征各个站点与背景变化之间的时序异步关系和峰值/累积浓度关系;最后,利用时间序列聚类方法,识别站点间关系较为一致的外源性颗粒物污染过程,作为对外来颗粒物在近地表运动模式的表征。2)提取该区域内典型的住区建筑群空间形态模式。首先,采集并配准研究区域建筑轮廓及层数数据和住区边界数据;随后,利用数学形态学方法,表征住区中普遍存在的不规则空间形态,建立描述每栋建筑和每个住区的形态指标集;最后,面向上述高维、稀疏数据集,利用CLIQUE子空间聚类算法,确定研究区内住区建筑群空间形态的共性特征和典型模式。3)组合形成典型场景并计算其关键物理场。根据典型外源性颗粒物污染过程和住区空间形态,形成少量的但能覆盖研究区域内大部分实际场景的算例,并利用湍流模型和离散相模型下的CFD(Computational Fluid Dynamics)模拟,考察上述场景下住区内颗粒物分布和物理场工况。4)提出根据模拟结果,提出低成本、高可行性的提升改善措施。根据模拟所得颗粒物浓度场及运动轨迹,具体针对每一种住区空间形态,顾及不同污染过程,从空间形态优选和调整、路网和游园规划设计、植被配置以及停车场选址等方面给出明确建议。本文以武汉市为研究区,并基于上述一系列的研究发现:1)通过对实际颗粒物污染过程和住区空间形态展开分析和模式提取,可以以少量仿真模拟实验直接服务于大量实际场景。研究区主要存在6种典型颗粒物污染过程和5种典型住区空间形态模式;交叉组合2种主导性的颗粒物污染过程和10种典型住区空间形态或其变形形式,就能为研究区内的住区环境改善提供直接参考。2)集成离散小波分解和动态时间规整的分析方法(DWT-DTW方法),能有效解决分钟级近地表监测网络所面临的复杂干扰和动态异步问题。建成环境下近地表颗粒物浓度时序波动主要由特征尺度在0.5小时以下、1～8小时以及1～2天的3个主要频率成分构成,外源性颗粒物污染过程一般为小时级或天际波动,但需要至少5～15分钟的采样间隔才能准确表征其时空过程。集成离散小波分解和动态时间规整,准确捕捉到历次颗粒物浓度上升过程下不同位置站点的响应差异,这种差异与颗粒物来源方向和潜在运动过程表现出明确联系。因此,DWT-DTW方法是识别表征外源性颗粒物污染过程的有效手段。3)基于数学形态学和CLIQUE子空间聚类的住区空间形态分析方法(MM-CLIQUE方法),能够有效表征具有不规则形态的城市住区,并提取其典型模式。实证分析表明,研究区域内住区主要由板式、不规则近似板式和双品字形建筑构成,并存在大量斜向夹角、半围合场所等多尺度、不规则空间形态特征。数学形态学算法能够有效提取上述特征,而CLIQUE子空间聚类算法则能实现高维形态特征的聚合,最终协同实现住区空间形态典型模式的提取。4)利用外源性颗粒物污染过程下住区人行高度颗粒物运动轨迹及其浓度场空间异质性,能够实现顾及成本和实践可行性的综合提升改善措施。模拟证实,同一污染过程下,不同住区形态模式之间人行高度浓度差异可达1倍左右,住区内部浓度差异更可达3～5倍。因此,选择合适的住区空间形态模式或者有效引导居民流线,都能够显著降低外来颗粒物对居民的负面影响。同时还证实,外来颗粒物的运动受到建筑壁面和周边气流的阻挡和限制,在大多数场景会有数个明确的出入通道;在住区内部和周边,也会存在颗粒物传输的若干关键位点。因此,针对不同的关键通道、关键位点,在一定的高度上采取阻拦或者疏导措施,能够主动地减少外来颗粒物进入住区或防止其在住区内滞留。总体而言,本文提出和实现了一种针对外源性颗粒物污染、旨在降低作用地区居民受外来颗粒物负面影响的住区提升改善路径,并开发了支持这一路径所需要的方法、技术和工具。研究给出的实证结果和相关分析结论,能够为武汉市和其它条件相近的城市中住区规划建设和更新改造工作提供直接参考。借助本研究的成果,空间规划学科可以在住区规划设计、旧城改造等阶段,以一种迭代优化周期短、负面作用微小、实施成本相对低廉的方式介入大气颗粒物污染的末端治理。},
  collaborator = {{詹庆明}},
  langid = {chinese},
  school = {武汉大学},
  keywords = {,/unread,CFD,PM2.5},
  annotation = {major: 城乡规划学\\
download: 171\\
CLC: X513\\
dbcode: CDFD\\
dbname: CDFDLAST2023\\
filename: 1023497795.nh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/C7RVTSPP/范域立 - 2023 - 住区户外空间受外源性颗粒物影响及提升改善研究.pdf}
}

@phdthesis{FuDuoMinJiYuJuanJiShenJingWangLuoDeHuaFenTuXiangShiBieFangFaYanJiu2022,
  type = {{硕士学位论文}},
  title = {{基于卷积神经网络的花粉图像识别方法研究}},
  author = {付多民},
  year = {2022},
  address = {唐山},
  abstract = {花粉的识别在孢粉学、法庭科学和古气候重建等领域中都发挥着重要的作用。目前,大部分花粉图像的自动化识别精度较低,且花粉图像识别模型的训练时间较长。因此,实现花粉图像识别模型的高精度和快速识别具有重要的意义。基于公开花粉图像数据集POLEN23E和POLLEN73S,引入卷积神经网络对花粉图像进行分类识别,重点研究了花粉图像的数据增强、参数优化方法以及花粉图像识别模型的构建。主要研究内容如下:1)为了保证实验的可靠性与真实性,采用随机选取的方式,使用K折交叉验证的方法对花粉图像数据进行划分,并采用图像翻转、色彩调节、Mix Up和Grid Mask等数据增强的方法对原始图像进行处理,抑制模型的过拟合情况,从而提高模型的测试精度。2)针对花粉图像清晰度低、形状相近等特性所导致的识别精度较低的问题,提出一种基于动态高效网络的花粉图像识别模型。首先,在Image Net数据集上使用Noisy Student方法对Efficient Net进行预训练;然后,将训练后的权重迁移到花粉识别模型中;最后,引入动态学习率提升模型的识别精度。仿真结果表明,基于动态高效网络的花粉图像识别模型具有很高的分类精度。3)从模型的训练时间角度出发,提出了一种基于改进残差网络的花粉图像识别模型。首先,通过添加Dropout层等方法,对Res Net50进行模型微调;然后,将模型中标准的卷积替换为空洞卷积;最后,将模型中Relu激活函数替换为Mish激活函数。仿真结果表明,基于改进残差网络的花粉图像识别模型能够使用较少的训练时间,并达到令人满意的分类效果。4)为了更好地满足用户对于简约性、快捷性和人性化的需求。因此,设计了具有较好的交互效果的花粉图像识别平台。该平台软件主要包括用户注册登录、花粉图像数据选择、花粉图像识别模型训练、花粉图像识别模型测试验证、花粉图像识别等5个模块。通过实际验证,该平台为花粉图像的自动化识别提供了便捷的识别工具。图35幅;表11个;参55篇。},
  langid = {chinese},
  school = {华北理工大学},
  keywords = {,/unread},
  annotation = {citation: 3},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/YK65QMLX/基于卷积神经网络的花粉图像识别方法研究_付多民.pdf}
}

@misc{GeometricDataAugmentations,
  title = {Geometric Data Augmentations to Mitigate Distribution Shifts in Pollen Classification from Microscopic Images},
  journal = {ar5iv},
  urldate = {2025-02-16},
  abstract = {Distribution shifts are characterized by differences between the training and test data distributions. They can significantly reduce the accuracy of machine learning models deployed in real-world scenarios. This paper {\dots}},
  howpublished = {https://ar5iv.labs.arxiv.org/html/2311.11029},
  langid = {english},
  keywords = {/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/3I2B37PZ/2311.html}
}

@misc{GuoJiaJiKongJuGongGongChangSuoWeiShengJianYanFangFaDi3BuFenKongQiWeiShengWu2013,
  title = {{公共场所卫生检验方法 第3部分：空气微生物}},
  author = {{国家疾控局}},
  year = {2013},
  month = dec,
  number = {GB/T 18204},
  urldate = {2025-08-25},
  langid = {chinese},
  keywords = {/unread},
  annotation = {CCS: C51\\
ICS: 13.060\\
applyDate: 2014-12-01},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/XJICDGRQ/newGbInfo.html}
}

@misc{GuoJiaWeiShengJianKangWeiYiYuanXiaoDuWeiShengBiaoZhun2012,
  title = {{医院消毒卫生标准}},
  author = {{国家卫生健康委}},
  year = {2012},
  month = jun,
  number = {GB 15982---2012},
  urldate = {2025-08-25},
  langid = {chinese},
  keywords = {/unread},
  annotation = {CCS: C59\\
ICS: 11.080\\
applyDate: 2012-11-01},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/Z6B37TCY/newGbInfo.html}
}

@phdthesis{GuoQianDaQiKeLiWuBaoLuYuYunDongDuiRenQunFeiGongNengJiShengWuBiaoZhiWuDeJiaoHuYingXiang2024,
  type = {{博士学位论文}},
  title = {{大气颗粒物暴露与运动对人群肺功能及生物标志物的交互影响}},
  author = {{郭倩}},
  year = {2024},
  journal = {工程科技I辑;医药卫生科技},
  address = {北京},
  doi = {10.26945/d.cnki.gbjku.2023.000402},
  urldate = {2025-08-22},
  abstract = {大气颗粒物暴露会损害肺功能,同时引起炎症反应和氧化应激,而运动会改善肺功能,并引起生物标志物的改变,探索二者交互影响成为当前研究热点。然而,现有研究并不充分,且研究结果并不一致。本研究以暴露剂量作为同时表征颗粒物暴露和运动强度的指标,通过心肺运动实验建立了心率-呼吸量模型,定量评估了个体实时呼吸量。开展交叉实验（cross-over）,监测了粒径小于1 {$\mu$}m的颗粒物（PM\textsubscript{1}）、细颗粒物(PM\textsubscript{2.5})、粗颗粒物(PM\textsubscript{10})和超细颗粒物（UFP）的浓度,定量评估了颗粒物的暴露剂量。基于心率评估了运动强度,测量了肺功能和呼出气一氧化氮（FeNO）,并检测了血液、尿液和呼出气冷凝液中的炎症标志物和氧化应激指标,探讨了颗粒物暴露和运动对肺功能及生物标志物的影响。主要研究结果如下:（1）在我国年轻人群中建立了心率-呼吸量模型,即Log（呼吸量）(L/min)=0.007{\texttimes}心率（bpm）+0.585,模型决定系数（R\textsuperscript{2}）为0.84,交叉验证R\textsuperscript{2}为0.78,均方根误差（RMSE）为0.13,表明模型具有良好的外推性;基于该模型量化了个体的暴露剂量,作为表征颗粒物和运动共同作用的指标。（2）运动可以削弱甚至逆转颗粒物暴露对肺功能的危害。PM\textsubscript{10}每升高一个四分位距（IQR）,非运动组人群中用力呼出75\%肺活量的瞬间流量（FEF75）下降了 0.1\%,而运动组人群中FEF75则升高了 5.5\%。（3）运动会削弱颗粒物暴露导致的气道炎症反应,然而会加剧颗粒物对系统性炎症细胞以及丙二醛（MDA）的影响。PM\textsubscript{10}每升高一个IQR,非运动组的FeNO升高0.2\%,运动组人群的FeNO则降低了 0.2\%。PM\textsubscript{1}每升高一个IQR,非运动组的单核细胞和嗜碱性粒细胞分别降低了 4.3\%和5.8\%,MDA增加了 1 0.4\%,而运动组人群的相关指标则分别升高了 4.4\%、1.4\%和14.8\%。（4）不同粒径颗粒物对肺功能及气道炎症生物标志物的最强效应发生在呼吸道的不同区域,PM\textsubscript{1}的最强效应发生在气管支气管（TB）区域,PM25的最强效应发生在胸外（ET）或TB区域,PM\textsubscript{10}的最强效应发生在ET区域。本研究可为探索颗粒物暴露和运动对健康的交互影响提供新的研究证据,为公众开展户外运动提供科学依据和支撑。},
  collaborator = {{段小丽}},
  langid = {chinese},
  school = {北京科技大学},
  keywords = {,/unread},
  annotation = {major: 环境科学与工程\\
download: 993\\
CLC: X513;R122\\
dbcode: CDFD\\
dbname: CDFDLAST2024\\
filename: 1023034204.nh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/4VDGY7WJ/郭倩 - 2024 - 大气颗粒物暴露与运动对人群肺功能及生物标志物的交互影响.pdf}
}

@phdthesis{GuoXinYuJiYuXianWeiTuXiangDeQiChuanHuaFenZiDongJianCeXiTongYanJiuYuShiXian2022,
  type = {{硕士学位论文}},
  title = {{基于显微图像的气传花粉自动检测系统研究与实现}},
  author = {郭昕雨},
  year = {2022},
  address = {天津},
  langid = {chinese},
  school = {天津大学},
  keywords = {/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/8VNJFVMK/基于显微图像的气传花粉自动检测系统研究与实现_郭昕雨.pdf}
}

@phdthesis{HanJinKeJiGuangSanSheFaZaiXianCeLiangKeLiWuZhiLiangNongDuYanJiu2021,
  type = {{博士学位论文}},
  title = {{激光散射法在线测量颗粒物质量浓度研究}},
  author = {{韩金克}},
  year = {2021},
  address = {武汉},
  doi = {10.27157/d.cnki.ghzku.2021.006252},
  abstract = {目前,燃煤电站颗粒物排放呈现粒径小、浓度低的特点,在排放监测强实时性和高准确性的要求下,颗粒物质量浓度在线测量面临重大挑战。激光散射法响应速度快、测量结构简单,尤其是对低浓度颗粒物测量具有良好的响应特性,在燃煤电站低浓度颗粒物质量浓度在线测量领域得到了广泛应用。光散射法测量颗粒物质量浓度的基本原理是当颗粒特性稳定不变时散射光强与颗粒物质量浓度呈正比,测量环境和颗粒特性的改变会造成光散射测量结果的失真。燃煤电站烟气相对湿度高且排放颗粒粒径特征易随电站运行状况波动,光散射法的简单比对标定难以满足燃煤电站颗粒物质量浓度的长期有效测量。针对以上问题,本文搭建了颗粒物质量浓度在线测量平台,可实现颗粒物质量浓度的实时测量（时间分辨率1 s）;探究相对湿度对光散射法测量的影响机制和多分散颗粒平均粒径对光散射法测量的影响规律;在研究基础上,进一步形成了多种基于粒径修正的颗粒物质量浓度在线测量方法;最后根据实际测量需求构建了光散射耦合{$\beta$}射线法颗粒物质量浓度在线测量模型,并进行了实验室测量研究。首先,探究了烟气湿度对光散射法测量的影响规律。搭建了含湿气溶胶颗粒物质量浓度在线测试平台;实验测试结果显示不同类型气溶胶的单位质量浓度散射光强（质量浓度灵敏度）对相对湿度的响应特性差异明显。通过实验和理论计算揭示了相对湿度对光散射法颗粒物质量浓度测量的影响机理:含湿气溶胶颗粒物的团聚和吸湿长大是相对湿度影响光散射测量的根本原因,两者对光散射法测量的作用相反,相对湿度对光散射法测量的影响效果取决于团聚作用和吸湿作用的相对强弱,与颗粒物吸湿特性和气溶胶相对湿度有关。最后在多种入射波长和相对湿度条件下,探究了光散射测量不同类型颗粒物时波长对气溶胶相对湿度变化的敏感性,为工业应用中含湿气溶胶颗粒物质量浓度的在线测量提供数据支持。其次,提出了基于波长散射比因子修正颗粒物质量浓度在线测量方法。在明确了相对湿度对光散射测量影响的本质依然是颗粒粒径的变化后,对不同粒径特征颗粒物的角散射特性进行了测量。结果表明针对同一散射角,颗粒物质量浓度灵敏度随粒径增大逐渐减小,不同散射角颗粒物质量浓度灵敏度对粒径变化的响应特性不同。定义对称散射角45{$^\circ$}与135{$^\circ$}散射光强的比值为散射非对称因子,发现非对称因子与颗粒平均粒径线性相关。进一步以平均粒径为媒介,以非对称因子在线修正颗粒物质量浓度灵敏度。实验测量结果显示,对于Si O2颗粒、燃煤飞灰和燃生物质...},
  collaborator = {{刘小伟}},
  langid = {chinese},
  school = {华中科技大学},
  keywords = {,/unread},
  annotation = {CNKICite: 11},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/Z7MM8EW7/激光散射法在线测量颗粒物质量浓度研究_韩金克.pdf}
}

@phdthesis{HanJinKeJiGuangSanSheFaZaiXianCeLiangKeLiWuZhiLiangNongDuYanJiu2021a,
  type = {{博士学位论文}},
  title = {{激光散射法在线测量颗粒物质量浓度研究}},
  author = {{韩金克}},
  year = {2021},
  address = {武汉},
  doi = {10.27157/d.cnki.ghzku.2021.006252},
  abstract = {目前,燃煤电站颗粒物排放呈现粒径小、浓度低的特点,在排放监测强实时性和高准确性的要求下,颗粒物质量浓度在线测量面临重大挑战。激光散射法响应速度快、测量结构简单,尤其是对低浓度颗粒物测量具有良好的响应特性,在燃煤电站低浓度颗粒物质量浓度在线测量领域得到了广泛应用。光散射法测量颗粒物质量浓度的基本原理是当颗粒特性稳定不变时散射光强与颗粒物质量浓度呈正比,测量环境和颗粒特性的改变会造成光散射测量结果的失真。燃煤电站烟气相对湿度高且排放颗粒粒径特征易随电站运行状况波动,光散射法的简单比对标定难以满足燃煤电站颗粒物质量浓度的长期有效测量。针对以上问题,本文搭建了颗粒物质量浓度在线测量平台,可实现颗粒物质量浓度的实时测量（时间分辨率1 s）;探究相对湿度对光散射法测量的影响机制和多分散颗粒平均粒径对光散射法测量的影响规律;在研究基础上,进一步形成了多种基于粒径修正的颗粒物质量浓度在线测量方法;最后根据实际测量需求构建了光散射耦合{$\beta$}射线法颗粒物质量浓度在线测量模型,并进行了实验室测量研究。首先,探究了烟气湿度对光散射法测量的影响规律。搭建了含湿气溶胶颗粒物质量浓度在线测试平台;实验测试结果显示不同类型气溶胶的单位质量浓度散射光强（质量浓度灵敏度）对相对湿度的响应特性差异明显。通过实验和理论计算揭示了相对湿度对光散射法颗粒物质量浓度测量的影响机理:含湿气溶胶颗粒物的团聚和吸湿长大是相对湿度影响光散射测量的根本原因,两者对光散射法测量的作用相反,相对湿度对光散射法测量的影响效果取决于团聚作用和吸湿作用的相对强弱,与颗粒物吸湿特性和气溶胶相对湿度有关。最后在多种入射波长和相对湿度条件下,探究了光散射测量不同类型颗粒物时波长对气溶胶相对湿度变化的敏感性,为工业应用中含湿气溶胶颗粒物质量浓度的在线测量提供数据支持。其次,提出了基于波长散射比因子修正颗粒物质量浓度在线测量方法。在明确了相对湿度对光散射测量影响的本质依然是颗粒粒径的变化后,对不同粒径特征颗粒物的角散射特性进行了测量。结果表明针对同一散射角,颗粒物质量浓度灵敏度随粒径增大逐渐减小,不同散射角颗粒物质量浓度灵敏度对粒径变化的响应特性不同。定义对称散射角45{$^\circ$}与135{$^\circ$}散射光强的比值为散射非对称因子,发现非对称因子与颗粒平均粒径线性相关。进一步以平均粒径为媒介,以非对称因子在线修正颗粒物质量浓度灵敏度。实验测量结果显示,对于Si O2颗粒、燃煤飞灰和燃生物质...},
  collaborator = {{刘小伟}},
  langid = {chinese},
  school = {华中科技大学},
  keywords = {,/unread},
  annotation = {CNKICite: 11},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/86KUZ6XL/激光散射法在线测量颗粒物质量浓度研究_韩金克.pdf}
}

@phdthesis{HanLiPingHuaFenTuXiangWenLiTeZhengTiQuFangFaDeYanJiu2019,
  type = {{硕士学位论文}},
  title = {{花粉图像纹理特征提取方法的研究}},
  author = {韩丽萍},
  year = {2019},
  address = {南京},
  abstract = {花粉颗粒的分类识别在花粉过敏控制、刑事侦查、石油勘探以及古气候重建等领域有着重要应用。但传统的花粉颗粒分类识别方法主要是依靠显微镜的人工目视检查,需要操作者具有丰富的孢粉形态学专业知识,鉴别过程耗时费力且易受操作者主观意识影响,准确度普遍不高。鉴于显微镜下的花粉图像有着跟普通图像类似的结构、纹理特征,利用计算机对花粉颗粒进行分类识别已经成为花粉鉴别的有效手段。但现有的花粉图像分类识别方法仍存在些许不足,主要包括以下两方面:现有描述子大多对噪声敏感、对花粉图像的旋转缩放没有较好的鲁棒性;多数描述子将多种特征融合,旨在利用不同特征的优点来构建花粉图像的最优表示,但这也大大增加了算法的时间复杂度,不利于花粉图像的实际分类识别。针对以上问题,本文对花粉图像的纹理特征提取方法进行研究,主要的研究内容包括:(1)针对传统局部二进制模式(LBP)有着对噪声敏感、对图像旋转变化的鲁棒性不高等问题,对传统局部二进制模式进行改进,提出一种基于主梯度编码的局部二进制模式,并将其应用于花粉图像的分类识别。该方法首先计算图像块在主梯度方向上的梯度幅值;其次,分别计算图像块的径向、角向以及复合梯度差;然后,根据各图像块的梯度差进行二进制编码,采用自适应权重分配策略为二进制编码自适应分配权重,并计算花粉图像在径向、角向以及复合方向上的LBP特征直方图;最后,将不同尺度下的纹理特征直方图融合,将融合特征用于花粉图像的分类识别。实验结果表明,该方法对花粉图像的噪声、旋转和缩放具有较好的鲁棒性。(2)针对花粉图像的纹理变化范围相较于普通纹理图像较小,过宽的量化区间难以捕捉不同花粉图像的细微纹理差异的问题,提出一种局部十进制模式(LDP),并将其应用于花粉图像的分类识别。该方法通过增加量化区间的数量、缩小量化区间的范围来捕获花粉图像的细微纹理差异,首先将花粉图像的梯度图像分解到8个方向,找出最大、最小以及中位梯度方向;其次,计算像素块在各梯度方向上的梯度幅值;然后,根据各量化区间内的像素块的数量进行十进制编码;最后,计算最大、最小以及中位梯度方向上的LDP特征直方图,将融合的特征用于花粉图像的分类识别。实验结果表明,该方法的平均正确识别率能达到90\%以上,且识别效率也高于部分对比方法。},
  langid = {chinese},
  school = {南京信息工程大学},
  keywords = {,/unread},
  annotation = {citation: 1},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/R5TFUFYF/花粉图像纹理特征提取方法的研究_韩丽萍.pdf}
}

@phdthesis{HaoJianLiTaiYuanShiXiaQiuJiQiChuanHuaFenJianCeJiGuoMinXingBiYanHuanZheBianYingYuanPuFenXi2023,
  type = {{硕士学位论文}},
  title = {{太原市夏秋季气传花粉监测及过敏性鼻炎患者变应原谱分析}},
  author = {郝建利},
  year = {2023},
  address = {太原},
  abstract = {目的:明确太原市夏秋季气传花粉的种类及播散规律及太原市夏秋季过敏性鼻炎患者过敏原分布情况;明确花粉与气象因子及过敏性鼻炎患者变应原检测人次的关系。方法:1.花粉采集将花粉采样设备(Durham取样器)放置在山西医科大学第一医院门诊部7楼楼顶,距地面约21米,地理位置位于东经112{$^\circ$}33{$\prime$}0{${''}$},北纬37{$^\circ$}51{$\prime$}14{${''}$},自2022年7月21日至2022年10月20日采用重力沉降法连续收集花粉。每天上午8点于采样器放置均匀涂有粘附剂的载玻片两张,至次日上午8点取片,使其全天暴露在空气中,取片后Callberla染色液染色加盖20mm{\texttimes}20mm盖玻片,光学显微镜下进行花粉颗粒的识别与计数,鉴别方法参照乔秉善撰写的《中国气传花粉和植物彩色图谱》。同时记录山西气象局官网每日预报的气象信息,包括温度、湿度、风力等气象数据。统计我院变态反应科同期变应原检测人数和变应原阳性人次,并记录到excel表中。2.变应原特异性Ig E抗体监测采集疑似过敏性鼻炎(allergic rhinitis,AR)患者空腹静脉血4ml,设置离心机3500 r/min,离心10min制备血清,采用全自动免疫印迹仪,根据吸入性和食入性变应原特异性Ig E抗体检测试剂盒(欧蒙印迹法)体外半定量检测受试者20种变应原s Ig E水平,其中包括10种吸入性变应原:(ts20)树木组合(柳树/榆树/杨树)、(w1)豚草、(w6)艾蒿、(dsl)室内尘螨组合(粉尘螨/屋尘螨)、(h1)屋尘、(e1)猫毛、(e2)狗上皮、(i6)德国蟑螂、(msl)点青霉/烟曲霉/分枝孢霉/交链孢霉、(u80)葎草;10种食入性变应原:(f1)蛋清、(f2)牛奶、(f13)花生、(f14)黄豆、(f27)牛肉、(f88)羊肉、(fs33)海洋鱼类组合(龙虾/鳕鱼/扇贝)、(f24)虾、(f23)蟹、(fs34)淡水鱼组合(鲑鱼/鲈鱼/鲤鱼),检测方法及步骤严格遵照说明书完成。所有操作均由同一名专业技师执行。结果:1.在为期92天(2022年7月21日至2022年10月20日)的气传花粉连续曝片中,共收集到花粉17118粒,其中曝片镜检出的花粉17107粒,鉴定到14科10属4种,未识别的花粉11粒。太原市夏秋季气传花粉飘散以杂草和禾本类为主,主要包括蒿属(66.62\%)、大麻/葎草属(17.79\%)、国槐(8.18\%)、藜/苋科(2.83\%)、禾本科(2.11\%),累计总量占花粉总...},
  langid = {chinese},
  school = {山西医科大学},
  keywords = {,/unread,IgE},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BYP8PXQR/太原市夏秋季气传花粉监测及过敏性鼻炎患者变应原谱分析_郝建利.pdf}
}

@article{heoEnrichedAerosoltohydrosolTransfer2021,
  title = {Enriched Aerosol-to-Hydrosol Transfer for Rapid and Continuous Monitoring of Bioaerosols},
  author = {Heo, Ki Joon and Ko, Hyun Sik and Jeong, Sang Bin and Kim, Sang Bok and Jung, Jae Hee},
  year = {2021},
  month = jan,
  journal = {Nano Letters},
  volume = {21},
  number = {2},
  pages = {1017--1024},
  issn = {1530-6984},
  doi = {10.1021/acs.nanolett.0c04096},
  urldate = {2025-08-21},
  abstract = {Bioaerosols, including infectious diseases such as COVID-19, are a continuous threat to global public safety. Despite their importance, the development of a practical, real-time means of monitoring bioaerosols has remained elusive. Here, we present a novel, simple, and highly efficient means of obtaining enriched bioaerosol samples. Aerosols are collected into a thin and stable liquid film by the unique interaction of a superhydrophilic surface and a continuous two-phase centrifugal flow. We demonstrate that this method can provide a concentration enhancement ratio of {$\sim$}2.4 {\texttimes} 106 with a collection efficiency of {$\sim$}99.9\% and an aerosol-into-liquid transfer rate of {$\sim$}95.9\% at 500 nm particle size (smaller than a single bacterium). This transfer is effective in both laboratory and external ambient environments. The system has a low limit of detection of {$<$}50 CFU/m3air using a straightforward bioluminescence-based technique and shows significant potential for air monitoring in occupational and public-health applications.},
  langid = {american},
  lccn = {2},
  keywords = {/unread},
  annotation = {JCR分区: Q1\\
中科院分区升级版: 材料科学2区\\
中科院分区基础版: 工程技术1区\\
影响因子: 9.1\\
5年影响因子: 9.9\\
EI: 是\\
TLDR: A novel, simple, and highly efficient means of obtaining enriched bioaerosol samples using a straightforward bioluminescence-based technique and shows significant potential for air monitoring in occupational and public-health applications.},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/JEY2LTQF/Heo 等 - 2021 - Enriched Aerosol-to-Hydrosol Transfer for Rapid and Continuous Monitoring of Bioaerosols.pdf}
}

@phdthesis{LiLinZeJiYuLandsatYaoGanYingXiangDeZhiBeiWuHouJianCeJiXiaoChuanZhuYuanFengXianGuanLianXingFenXi2021,
  type = {{博士学位论文}},
  title = {{基于Landsat遥感影像的植被物候监测及哮喘住院风险关联性分析}},
  author = {{李林泽}},
  year = {2021},
  address = {武汉},
  doi = {10.27379/d.cnki.gwhdu.2021.002235},
  abstract = {哮喘病已经成为当今世界上最为普遍的疾病之一。其中,由春季花粉引起的哮喘发病风险占据重要比重。然而,由于花粉浓度监测站点较少、过敏原数据难以获取等问题,使用传统的地表观测手段往往不能满足花粉对哮喘病暴露风险的研究需要。近年来不断有学者尝试利用遥感植被物候信息反映地表植被的生长规律,但较少研究论证长时间序列遥感植被春季物候与春季花粉释放周期(起始时期,周期长度等)的时空相关性,以及在气候环境变化条件下遥感植被物候动态变化与哮喘病住院风险的关联性。本文从导致春季哮喘病住院的主要诱因(花粉)入手,研究分析了利用遥感植被春季物候信息预测花粉释放起始时期及其动态变化的可行性,为大区域尺度哮喘病住院风险评估提供了数据支持。在此基础上,研究了遥感植被春季物候的动态变化对哮喘病住院风险的影响,为环境流行病学研究提供了新的视角。基于遥感影像的植被物候模型日趋成熟。但是,随着对不同植被物候活动的长时间、精细化监测需要,以及植被物候对微生态环境、人类健康影响的研究需求,提高遥感植被物候监测的空间分辨率和模型预测精度越来越重要。此外,由于使用单一的遥感植被物候信息很难全面反映实际的地表植被物候活动规律,综合考虑包含遥感影像在内的多源数据(如温度,降水、光照、土壤含水量等),结合气候学、物候学等不同学科优势,建立多源数据融合模型是提高地表植被物候预测精度的关键。提高遥感植被物候模型的预测精度能为哮喘病住院风险研究提供数据保障,使用高质量的植被物候信息也能够在植被春季物候动态变化与哮喘病住院风险关联性分析中得到更加真实可信的研究成果。本文围绕遥感植被物候监测关键技术以及遥感植被物候与哮喘病住院风险关联性分析,研究分析了遥感植被物候模型在Landsat影像中提取春季物候信息的不确定性及其受到气候环境因素的影响;建立了提高地物光谱信息预测精度的遥感影像时空融合模型;建立了提高了地表植被物候信息预测精度的多源数据融合模型;研究了遥感植被春季物候与哮喘病住院风险关联性分析的理论合理性和技术可行性。本文的主要研究内容和成果归纳如下:1.研究了遥感植被物候模型在Landsat影像中提取植被物候信息的技术流程,深入分析了遥感植被物候模型在提取逐年植被春季物候信息的不确定性。为了消除气候环境变化对不同时间周期Landsat遥感影像提取的植被春季物候信息的影响,本文也研究分析了遥感植被春季物候信息在逐年气候环境变化(温度和降雨量)中的响应。本文选取了北美...},
  collaborator = {{李建松} and Sapkota, Amir},
  langid = {chinese},
  school = {武汉大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HJLWS2KP/基于Landsat遥感影像的植被物候监测及哮喘住院风险关联性分析_李林泽.pdf}
}

@phdthesis{LiMengYingKeNingJieKeLiWuDuiZhongGuoYouJiWuJiQiRongXiaoJipm25YingXiangDeShuZhiMoNiYanJiu2023,
  type = {{博士学位论文}},
  title = {{可凝结颗粒物对中国有机、无机气溶胶及pm2.5影响的数值模拟研究}},
  author = {{李梦莹}},
  year = {2023},
  address = {杭州},
  doi = {10.27461/d.cnki.gzjdx.2023.003011},
  abstract = {准确估计大气污染物排放不仅是驱动化学传输模式,可靠复现大气污染过程的核心基础,更是有效开展大气污染防控的关键前提。原位监测已证实,固定燃烧源排放了远高于可过滤颗粒物的可凝结颗粒物（CPM）。然而截至目前,大气污染物排放清单大多未考虑CPM,我国CPM排放的时空演化特征及其对大气污染的影响尚不明晰。本文聚焦这一重要科学问题,梳理了固定燃烧源CPM排放的原位监测结果,估算了我国2014和2017年CPM的排放量,包括有机和无机组分;在此基础上,运用气象--化学传输模型（WRF--CMAQ）,建立了适应于气溶胶模块参数化方案的输入排放清单,开展敏感性数值模拟,定量评估了CPM对大气有机气溶胶、无机盐离子和细颗粒物(PM2.5)的显著贡献,并进一步阐明了在考虑CPM的背景下,气溶胶--辐射相互作用（ARI）对关键气象要素（地面短波辐射、边界层高度、温度和相对湿度）、PM2.5和臭氧时空演化特征的影响。本文量化了CPM排放及其环境影响,强调了CPM是PM2.5的一个重要来源,加深了发展CPM控制技术对缓解大气污染必要性的认识,可为提升污染控制策略提供科学指导。主要研究内容及结果如下:（1）建立了中国固定燃烧源CPM排放清单。加入CPM使得我国2014和2017年总有机质的年排放量较原始清单有机质排放量均增加近2倍。电厂、钢铁厂和工业燃烧源对总有机质排放量的贡献最大,超过65\%。2014和2017年CPM中总水溶性无机离子的年排放量均比原始清单中无机离子排放量高约4倍。其中钢铁厂的贡献最大（2014年为35\%,2017年为43\%）,而水泥厂和其他工艺过程源的贡献最小。同时充分考虑不同来源类别CPM排放的差异性,运用自展和蒙特卡洛方法,开展不确定性分析,为后续研究CPM的影响提供基础数据。（2）量化了CPM有机组分对不同污染时期一次有机气溶胶（POA）、二次有机气溶胶（SOA）及PM2.5的贡献,揭示了CPM是形成大气POA和SOA的重要来源,有效改善了目前研究广泛报道的SOA模拟低估的现象,较好再现了观测峰值。有机CPM对2014年10月14日～11月14日北京站点POA和SOA小时模拟浓度的贡献分别为49\%和58\%。CPM贡献使得有机气溶胶浓度呈现不同程度的区域性上升。对2018年12月6～30日京津冀及其周边``2+26''城市的分析也证实...},
  collaborator = {{俞绍才}},
  langid = {chinese},
  school = {浙江大学},
  keywords = {,-,/unread,PM2.5},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/8BLU6JR7/可凝结颗粒物对中国有机、无机气溶胶及PM_（2.5）影响的数值模拟研究_李梦莹.pdf}
}

@phdthesis{LiMengYingKeNingJieKeLiWuDuiZhongGuoYouJiWuJiQiRongXiaoJipm25YingXiangDeShuZhiMoNiYanJiu2023a,
  type = {{博士学位论文}},
  title = {{可凝结颗粒物对中国有机、无机气溶胶及pm2.5影响的数值模拟研究}},
  author = {{李梦莹}},
  year = {2023},
  address = {杭州},
  doi = {10.27461/d.cnki.gzjdx.2023.003011},
  abstract = {准确估计大气污染物排放不仅是驱动化学传输模式,可靠复现大气污染过程的核心基础,更是有效开展大气污染防控的关键前提。原位监测已证实,固定燃烧源排放了远高于可过滤颗粒物的可凝结颗粒物（CPM）。然而截至目前,大气污染物排放清单大多未考虑CPM,我国CPM排放的时空演化特征及其对大气污染的影响尚不明晰。本文聚焦这一重要科学问题,梳理了固定燃烧源CPM排放的原位监测结果,估算了我国2014和2017年CPM的排放量,包括有机和无机组分;在此基础上,运用气象--化学传输模型（WRF--CMAQ）,建立了适应于气溶胶模块参数化方案的输入排放清单,开展敏感性数值模拟,定量评估了CPM对大气有机气溶胶、无机盐离子和细颗粒物(PM2.5)的显著贡献,并进一步阐明了在考虑CPM的背景下,气溶胶--辐射相互作用（ARI）对关键气象要素（地面短波辐射、边界层高度、温度和相对湿度）、PM2.5和臭氧时空演化特征的影响。本文量化了CPM排放及其环境影响,强调了CPM是PM2.5的一个重要来源,加深了发展CPM控制技术对缓解大气污染必要性的认识,可为提升污染控制策略提供科学指导。主要研究内容及结果如下:（1）建立了中国固定燃烧源CPM排放清单。加入CPM使得我国2014和2017年总有机质的年排放量较原始清单有机质排放量均增加近2倍。电厂、钢铁厂和工业燃烧源对总有机质排放量的贡献最大,超过65\%。2014和2017年CPM中总水溶性无机离子的年排放量均比原始清单中无机离子排放量高约4倍。其中钢铁厂的贡献最大（2014年为35\%,2017年为43\%）,而水泥厂和其他工艺过程源的贡献最小。同时充分考虑不同来源类别CPM排放的差异性,运用自展和蒙特卡洛方法,开展不确定性分析,为后续研究CPM的影响提供基础数据。（2）量化了CPM有机组分对不同污染时期一次有机气溶胶（POA）、二次有机气溶胶（SOA）及PM2.5的贡献,揭示了CPM是形成大气POA和SOA的重要来源,有效改善了目前研究广泛报道的SOA模拟低估的现象,较好再现了观测峰值。有机CPM对2014年10月14日～11月14日北京站点POA和SOA小时模拟浓度的贡献分别为49\%和58\%。CPM贡献使得有机气溶胶浓度呈现不同程度的区域性上升。对2018年12月6～30日京津冀及其周边``2+26''城市的分析也证实...},
  collaborator = {{俞绍才}},
  langid = {chinese},
  school = {浙江大学},
  keywords = {,-,/unread,PM2.5},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/9M3XP4IW/可凝结颗粒物对中国有机、无机气溶胶及PM_（2.5）影响的数值模拟研究_李梦莹.pdf}
}

@phdthesis{LinBiYiJiYuJiSuanJiShiJueDeHuaFenJianCeSuanFaDeYanJiuYuYingYong2020,
  type = {{硕士学位论文}},
  title = {{基于计算机视觉的花粉检测算法的研究与应用}},
  author = {林必艺},
  year = {2020},
  address = {天津},
  abstract = {气传花粉是常见的过敏原之一,严重危害着特定敏感人群的身体健康,因此,花粉浓度播报业务已经越来越成为气象局不可或缺的业务之一。目前常见的花粉浓度检测方式为首先对空气中的花粉进行收集,制作成花粉涂片,然后在显微镜下寻找并统计花粉的数量,使用统计结果推算空气中花粉浓度。人工识别和统计花粉数量存在效率低,成本高等缺点。本文首先采集显微镜下的花粉图像并构建成数据集,然后基于计算机视觉技术中的数字图像处理技术和深度学习技术,构建花粉检测模型,最后开发并部署花粉检测系统。 本文的主要工作归纳如下: (1)基于显微镜的电子目镜等硬件设备,开发花粉图像采集与标记系统,通过该采集系统对气象局提供的2019年的花粉涂片进行采集。采集完成后对图像中的花粉的位置进行标记和保存,构建成花粉图像数据集。 (2)分析所采集的花粉图像的特征,分别基于数字图像处理技术和深度学习技术,构建两个花粉检测模型。前者主要用到的算法有霍夫圆检测,区域生长,径向展开特征提取等。后者使用的深度目标检测模型为优化的YOLOv3模型。通过两个模型的融合和优化,形成最终使用的花粉检测模型。 (3)以Spring Boot后台框架和Bootstrap前端框架为主体,以检测模型为核心,开发并部署B/S架构的花粉图像在线检测系统。系统通过Redis数据库缓存登录凭据,实现用户单点登录的功能,增强了系统的安全性;通过Nginx反向代理服务器,实现检测服务横向扩展的能力。检测系统通过校园网分配的IPv6公网地址提供检测服务。},
  langid = {chinese},
  school = {天津大学},
  keywords = {,/unread,Spring Boot,YOLOv3},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/7EYGMETW/基于计算机视觉的花粉检测算法的研究与应用_林必艺.pdf}
}

@article{LiuChangHaiJiYuShuJuZengQiangHeTeZhengRongHeDeHuaFenTuXiangFenLei2025,
  title = {{基于数据增强和特征融合的花粉图像分类}},
  author = {刘昌海 and 张恒 and 陆小锋 and 冯予乐 and 吕森林 and 刘书朋},
  year = {2025},
  journal = {工业控制计算机},
  volume = {38},
  number = {1},
  pages = {97--99},
  issn = {1001-182X},
  abstract = {传统花粉分析识别方法依赖于人工经验标注花粉图像，这一过程耗时且耗费人力。近年来，深度学习技术取代传统方法，广泛应用于花粉分析识别研究领域。然而，目前深度学习方法在识别时间、内存占用和准确性等算法模型效能方面仍有待优化。为进一步提高算法模型的效能，采用了数据增强技术，通过旋转、缩放和平移等手段扩展花粉数据集并采用局部二值模式（LBP）网络实现特征融合，提升花粉识别的准确度。实验结果显示，该方法在花粉识别上取得了95.1\%的准确率和96.4\%的精确率，表明数据增强和特征融合技术在提高花粉识别准确性和效率方面的有效性，为花粉分析识别相关研究领域提供技术和方法参考。},
  langid = {chinese},
  lccn = {32-1764/TP},
  keywords = {,/unread,No DOI found},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/P9QYLBNU/基于数据增强和特征融合的花粉图像分类_刘昌海.pdf}
}

@phdthesis{LiuYuZhuZhaiShiNeiKeLiWuNongDuBianHuaJiShuYunTeXingYanJiu2021,
  type = {{博士学位论文}},
  title = {{住宅室内颗粒物浓度变化及输运特性研究}},
  author = {{刘昱}},
  year = {2021},
  address = {哈尔滨},
  doi = {10.27061/d.cnki.ghgdu.2021.005349},
  abstract = {人们平均有一半的时间在住宅中度过,住宅室内空气品质成为人们关心和关注的问题。由于空气中细颗粒物(空气动力学当量直径{$\leq$}2.5{$\mu$}m,PM2.5)能够被吸入到人体内部,携带着毒害物质对人体健康造成威胁,因此住宅室内颗粒物的研究也越来越受到重视。客厅作为住宅室内人员经常活动区域之一,客厅的颗粒物浓度对住宅室内空气品质起到重要作用。客厅颗粒物浓度受到多种污染源影响,厨房炊事导致客厅颗粒物污染非常严重,外窗渗透也能够导致客厅颗粒物浓度水平发生变化。目前,对住宅客厅颗粒物浓度分布及影响因素的相关研究较少。基于上述问题,本文通过文献调研、浓度监测、数值仿真等研究手段,对城市住宅客厅颗粒物质量浓度变化及颗粒物扩散特性进行研究,并对外窗渗透和厨房炊事下的室内细颗粒物浓度进行预测。首先,调研不同国家和地区城市住宅近二十年室内PM2.5质量浓度的状况,分析不同地区之间浓度水平的差异及浓度影响因素,发现我国室内PM2.5浓度水平较高,并受到室内活动类型、住宅通风方式、建筑类型、室外浓度水平和季节等因素的影响。据此,选取典型六户城市住宅,实测供暖季节室内PM0.5、PM1.0、PM2.5、PM5.0、PM10.0颗粒物质量浓度,得到住宅客厅不同粒径颗粒物的质量浓度变化规律。此外,统计监测时段内室外逐时PM2.5质量浓度,分析不同住宅室内及室外PM2.5质量浓度变化规律,分别研究外窗渗透和厨房炊事两个因素对住宅室内客厅PM2.5质量浓度的影响。其次,基于室内客厅PM2.5浓度的变化,理论分析炊事颗粒物从厨房到邻室客厅扩散输运机理,构建小型中式住宅厨房及邻室物理模型,为研究炊事过程对住宅室内环境的影响提供基础。此外,分析确定油温度、炊事颗粒物散发率、排油烟机排风量等边界条件参数取值,采用标准k-{$\varepsilon$}湍流模型和颗粒物离散相模型分别模拟室内气流和颗粒物的运动。对不同网格数模拟结果进行验算,选取得到最佳网格数。在和模型厨房尺寸布局一致的实际厨房中开展实验,分别监测油加热开始及结束两个阶段、距离地面1.5 m操作人员呼吸处的空气温度,实验验证最佳网格数下的数值计算结果,并和文献中实验工况条件相近...},
  collaborator = {{姜益强}},
  langid = {chinese},
  school = {哈尔滨工业大学},
  keywords = {,(PM2.5),/unread},
  annotation = {CNKICite: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/L7PAZMWQ/住宅室内颗粒物浓度变化及输运特性研究_刘昱.pdf}
}

@phdthesis{LiuYuZhuZhaiShiNeiKeLiWuNongDuBianHuaJiShuYunTeXingYanJiu2021a,
  type = {{博士学位论文}},
  title = {{住宅室内颗粒物浓度变化及输运特性研究}},
  author = {{刘昱}},
  year = {2021},
  doi = {10.27061/d.cnki.ghgdu.2021.005349},
  abstract = {人们平均有一半的时间在住宅中度过,住宅室内空气品质成为人们关心和关注的问题。由于空气中细颗粒物(空气动力学当量直径{$\leq$}2.5{$\mu$}m,PM2.5)能够被吸入到人体内部,携带着毒害物质对人体健康造成威胁,因此住宅室内颗粒物的研究也越来越受到重视。客厅作为住宅室内人员经常活动区域之一,客厅的颗粒物浓度对住宅室内空气品质起到重要作用。客厅颗粒物浓度受到多种污染源影响,厨房炊事导致客厅颗粒物污染非常严重,外窗渗透也能够导致客厅颗粒物浓度水平发生变化。目前,对住宅客厅颗粒物浓度分布及影响因素的相关研究较少。基于上述问题,本文通过文献调研、浓度监测、数值仿真等研究手段,对城市住宅客厅颗粒物质量浓度变化及颗粒物扩散特性进行研究,并对外窗渗透和厨房炊事下的室内细颗粒物浓度进行预测。首先,调研不同国家和地区城市住宅近二十年室内PM2.5质量浓度的状况,分析不同地区之间浓度水平的差异及浓度影响因素,发现我国室内PM2.5浓度水平较高,并受到室内活动类型、住宅通风方式、建筑类型、室外浓度水平和季节等因素的影响。据此,选取典型六户城市住宅,实测供暖季节室内PM0.5、PM1.0、PM2.5、PM5.0、PM10.0颗粒物质量浓度,得到住宅客厅不同粒径颗粒物的质量浓度变化规律。此外,统计监测时段内室外逐时PM2.5质量浓度,分析不同住宅室内及室外PM2.5质量浓度变化规律,分别研究外窗渗透和厨房炊事两个因素对住宅室内客厅PM2.5质量浓度的影响。其次,基于室内客厅PM2.5浓度的变化,理论分析炊事颗粒物从厨房到邻室客厅扩散输运机理,构建小型中式住宅厨房及邻室物理模型,为研究炊事过程对住宅室内环境的影响提供基础。此外,分析确定油温度、炊事颗粒物散发率、排油烟机排风量等边界条件参数取值,采用标准k-{$\varepsilon$}湍流模型和颗粒物离散相模型分别模拟室内气流和颗粒物的运动。对不同网格数模拟结果进行验算,选取得到最佳网格数。在和模型厨房尺寸布局一致的实际厨房中开展实验,分别监测油加热开始及结束两个阶段、距离地面1.5 m操作人员呼吸处的空气温度,实验验证最佳网格数下的数值计算结果,并和文献中实验工况条件相近...},
  collaborator = {{姜益强}},
  langid = {chinese},
  school = {哈尔滨工业大学},
  keywords = {,(PM2.5),/unread},
  annotation = {CNKICite: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/MSLBBPCA/住宅室内颗粒物浓度变化及输运特性研究_刘昱.pdf}
}

@phdthesis{NiuChaoJiYuGaoGuangPuYaoGanDeNeiLuShuiTiFuSheXiaoZhengYuShuiHuanJingCanLiangFanYan2025,
  type = {{博士学位论文}},
  title = {{基于高光谱遥感的内陆水体辐射校正与水环境参量反演}},
  author = {{牛超}},
  year = {2025},
  journal = {工程科技I辑},
  address = {上海},
  doi = {10.27149/d.cnki.ghdsu.2024.000206},
  urldate = {2025-08-22},
  abstract = {内陆水体在人类社会生存和可持续发展中发挥着重要作用,其生态环境质量是全球关注的热点问题。当前内陆水体受到人类活动和自然因素的多重威胁,导致水环境质量问题频发。高光谱遥感可以提供丰富的光谱信息,能够及时准确地反映水质基本状况与变化趋势,实现水环境精确监测。``航天-航空-地面''的多平台高光谱数据提供了水质监测的新途径,为快速、准确、大范围的高精度水环境参量制图提供了新的方案,在探究区域水体水环境特性及外部驱动因素、分析水质变化及污染响应机制、指导流域综合评价与治理等方面起到了重要作用。 针对高光谱遥感水环境监测存在的离水反射率计算不精确、水体固有光学量反演泛化性差、非光学活性水质参数建模精度低等问题,论文提出了多成像条件归一化的水体精确辐射校正算法,获取了精确的离水反射率;基于地面实测数据构建了经验参数优化的半解析固有光学量与叶绿素a反演方法,应用至多源、多时相高光谱遥感影像开展长时序高光谱反演研究;建立了基于深度特征提取的水质参数估算模型,实现了总氮、总磷、氨氮等水质参数空间分布制图与水环境分析,应用于长三角生态绿色一体化发展示范区水环境监测。 论文的主要研究工作和结论包括: （1）针对复杂水陆场景下的地物各向异性反射导致的辐射信息不一致的问题,提出了多成像条件归一化的水体精确辐射校正算法,消除了由姿态变化、成像几何、飞行方向等成像条件差异对水体辐射信号的影响,提升了研究区域高光谱影像获取离水反射信息的精度。结果表明,校正后的影像数据优化了双向反射分布函数（Bidirectional Reflectance Distribution Function,BRDF）效应引起的辐射不一致性,提升了真实地表反射率精度,校正后的影像反射率精度R\textsuperscript{2}到达0.95以上。对比多项式拟合方法、传统半经验核函数校正方法以及半经验水体BRDF校正方法,所提出的校正模型在目视效果和定量评价指标上均取得了较好的结果,其中重叠区域的水体反射率平均绝对偏差降低50\%以上。通过对不同区域和成像环境更为复杂的机载高光谱影像进行对比验证,证明了模型的适用性和准确性,为后续高精度水质参数定量反演与制图提供了精准的数据支撑。 （2）针对水色反演模型在不同传感器可迁移性和推广性难的问题,在准分析算法（Quasi-Analytical Algorithm,QAA）的基础上考虑水体辐射传输机理与区域水体光学特征,构建了区域参数优化的半解析固有光学量与叶绿素a反演模型。提出了一种双波段联合颗粒后向散射系数反演策略,通过优化求解过程,克服单一参考波长的局限性。引入高斯参数化方法对总吸收系数进行分解,减少了间接反演的中间步骤和误差,提取了浮游植物吸收系数,并结合遥感反射率构建了叶绿素a二元反演模型。基于地面实测光谱的总吸收系数、颗粒物后向散射系数、浮游植物吸收系数、叶绿素a的反演精度R\textsuperscript{2}分别可以达到0.9672,0.9649、0.8037和0.8283,应用于航空高光谱影像中反演精度R\textsuperscript{2}分别可以达到0.9590,0.7520、0.7727和0.7520。反演模型成功应用在机载和卫星高光谱影像中,实现了长三角一体化示范区多传感器、多时相固有光学量和叶绿素a的反演应用。 （3）针对非光学活性水质参数的光谱特征难以获取、制图精度低的问题,提出了基于深度特征提取的多水质参数估算方法,创建了基于高维注意力差值权重的深度卷积空谱联合学习方法,实现了基于高光谱影像的空谱敏感性特征优化。构建了基于影像块和通道注意力机制的深度回归网络,提升了内陆水体水质参数回归精度,并精确估算了总氮、总磷、氨氮三种水质参数分布情况,通过尺度分析、消融实验和模型对比验证,结果显示所提出模型具有良好的制图精度和稳定的回归性能,其中测试集精度R\textsuperscript{2}分别达到0.8315,0.8137和0.8245。对比传统基于特征分析与机器学习回归建模方法,测试集精度R\textsuperscript{2}提升约30\%。将模型应用至航空高光谱影像,完成了水质参数综合制图与``一河三湖''重点区域水质状况分析。结合国控断面数据对模型进行无样本区域及跨平台适用性分析,结果表明深度模型具有较好的可迁移性,但数据驱动的建模手段依赖于现有数据分布,依靠单期影像建立的非光学活性水质参数模型在长时序应用中存在一定局限。},
  collaborator = {{谭琨}},
  langid = {chinese},
  school = {华东师范大学},
  keywords = {,/unread,BRDF},
  annotation = {major: 地图学与地理信息系统\\
download: 803\\
CLC: X52\\
dbcode: CDFD\\
dbname: CDFDLAST2025\\
filename: **********.nh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/IHVBE4Y9/牛超 - 2025 - 基于高光谱遥感的内陆水体辐射校正与水环境参量反演.pdf}
}

@phdthesis{ShenMengChenShiNeiWaiKeLiWuZhongDianXingYouJiWuRanWuDeHuanJingXingWeiJiJianKangFengXianPingJie2023,
  type = {{博士学位论文}},
  title = {{室内外颗粒物中典型有机污染物的环境行为及健康风险评价}},
  author = {{沈梦晨}},
  year = {2023},
  address = {合肥},
  doi = {10.27517/d.cnki.gzkju.2023.000745},
  abstract = {由于人们绝大部分时间都处于相对较为封闭的小尺度的室内环境中,各式各样的污染物在室内环境中不易扩散,因此室内环境污染就成为了人类健康的重要影响因素之一。在中国,由于地理环境、污染源分布、污染特征、经济状况和生活习惯存在较大差异,人类接触室内空气污染物的程度以及室内和室外之间的关系尚未有充分的研究,同时也需要对室内/室外和个人暴露特征进行系统研究。因此本研究以室内外颗粒物为研究对象,系统研究室内外颗粒物的理化性质及暴露风险特征,解析室内颗粒物来源,对研究有机污染物在室内外环境中的迁移机制和制定城市地区的污染控制政策具有重要价值。本研究以合肥市为例,在不同功能区、不同楼层共采集室内颗粒物样品65个、室外颗粒物样品36个、建筑物外颗粒物样品7个。基于多种实验分析方法(快速溶剂萃取实验、激光粒度分析仪、扫描电镜-能谱仪、高分辨率气相色谱-质谱仪、元素分析仪-稳定同位素质谱仪等)以及数据分析方法(主成分分析、同位素示踪法、风险评价等),研究了室内颗粒物的形态特征、化学组成、典型有机污染物分布和富集特征、来源解析和风险评价。本研究工作的主要研究内容如下:(1)对比研究了室内外颗粒物的形态特征和物质组成的差异性,揭示了粒径和室内人类活动(做饭、抽烟等)对室内颗粒物中PAHs来源的影响作用。室内灰尘的粒径更集中在26-125{$\mu$}m(26-62{$\mu$}m和62-125{$\mu$}m)和498-837{$\mu$}m范围内;与室内颗粒物相比,室外颗粒物有部分小粒径({$<$}2.5{$\mu$}m)和大粒径({$>$}192{$\mu$}m)颗粒组成。室内颗粒物的元素分布结果表明除了含有C、O和Ca成分外,主要是富含Fe和Zr的颗粒;室内颗粒物的元素分布结果表明主要由Fe、O、Si、Al和Ca元素组成。(2)揭示了不同功能区室内颗粒物中典型有机污染物的分布、富集和赋存特征,发现合肥不同行政区域室内PAH均值的分布,其顺序如下:包河{$>$}经开{$>$}瑶海{$>$}蜀山{$>$}滨湖;OCPs最突出的浓度是Endosulfans和Cischlordane;不同功能区的{$\sum$}PCBs和{$\sum$}OCPs的浓度分别为蜀山(AD){$>$}包河/庐阳(CD){$>$}经开(ID){$>$}室外(OD)和蜀山(AD){$>$}包河/庐阳(CD){$>$}室外(OD){$>$}经开(ID)而递减。(3)研究了室内颗粒物中有机污染物来源分配特征,提出了结合C/N同位素示踪法、PCA、PMF、诊断比率等方法研究室内颗粒物中有点污染物的来源,发现室内外灰尘中的PAHs来源主要是汽车尾气、化石燃料和生物质燃烧...},
  collaborator = {{刘桂建}},
  langid = {chinese},
  school = {中国科学技术大学},
  keywords = {,/unread},
  annotation = {CNKICite: 1},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HRLXM5JS/室内外颗粒物中典型有机污染物的环境行为及健康风险评价_沈梦晨.pdf}
}

@phdthesis{ShenMengChenShiNeiWaiKeLiWuZhongDianXingYouJiWuRanWuDeHuanJingXingWeiJiJianKangFengXianPingJie2023a,
  type = {{博士学位论文}},
  title = {{室内外颗粒物中典型有机污染物的环境行为及健康风险评价}},
  author = {{沈梦晨}},
  year = {2023},
  address = {合肥},
  doi = {10.27517/d.cnki.gzkju.2023.000745},
  abstract = {由于人们绝大部分时间都处于相对较为封闭的小尺度的室内环境中,各式各样的污染物在室内环境中不易扩散,因此室内环境污染就成为了人类健康的重要影响因素之一。在中国,由于地理环境、污染源分布、污染特征、经济状况和生活习惯存在较大差异,人类接触室内空气污染物的程度以及室内和室外之间的关系尚未有充分的研究,同时也需要对室内/室外和个人暴露特征进行系统研究。因此本研究以室内外颗粒物为研究对象,系统研究室内外颗粒物的理化性质及暴露风险特征,解析室内颗粒物来源,对研究有机污染物在室内外环境中的迁移机制和制定城市地区的污染控制政策具有重要价值。本研究以合肥市为例,在不同功能区、不同楼层共采集室内颗粒物样品65个、室外颗粒物样品36个、建筑物外颗粒物样品7个。基于多种实验分析方法(快速溶剂萃取实验、激光粒度分析仪、扫描电镜-能谱仪、高分辨率气相色谱-质谱仪、元素分析仪-稳定同位素质谱仪等)以及数据分析方法(主成分分析、同位素示踪法、风险评价等),研究了室内颗粒物的形态特征、化学组成、典型有机污染物分布和富集特征、来源解析和风险评价。本研究工作的主要研究内容如下:(1)对比研究了室内外颗粒物的形态特征和物质组成的差异性,揭示了粒径和室内人类活动(做饭、抽烟等)对室内颗粒物中PAHs来源的影响作用。室内灰尘的粒径更集中在26-125{$\mu$}m(26-62{$\mu$}m和62-125{$\mu$}m)和498-837{$\mu$}m范围内;与室内颗粒物相比,室外颗粒物有部分小粒径({$<$}2.5{$\mu$}m)和大粒径({$>$}192{$\mu$}m)颗粒组成。室内颗粒物的元素分布结果表明除了含有C、O和Ca成分外,主要是富含Fe和Zr的颗粒;室内颗粒物的元素分布结果表明主要由Fe、O、Si、Al和Ca元素组成。(2)揭示了不同功能区室内颗粒物中典型有机污染物的分布、富集和赋存特征,发现合肥不同行政区域室内PAH均值的分布,其顺序如下:包河{$>$}经开{$>$}瑶海{$>$}蜀山{$>$}滨湖;OCPs最突出的浓度是Endosulfans和Cischlordane;不同功能区的{$\sum$}PCBs和{$\sum$}OCPs的浓度分别为蜀山(AD){$>$}包河/庐阳(CD){$>$}经开(ID){$>$}室外(OD)和蜀山(AD){$>$}包河/庐阳(CD){$>$}室外(OD){$>$}经开(ID)而递减。(3)研究了室内颗粒物中有机污染物来源分配特征,提出了结合C/N同位素示踪法、PCA、PMF、诊断比率等方法研究室内颗粒物中有点污染物的来源,发现室内外灰尘中的PAHs来源主要是汽车尾气、化石燃料和生物质燃烧...},
  collaborator = {{刘桂建}},
  langid = {chinese},
  school = {中国科学技术大学},
  keywords = {,/unread},
  annotation = {CNKICite: 1},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/WVTUZE8W/室内外颗粒物中典型有机污染物的环境行为及健康风险评价_沈梦晨.pdf}
}

@phdthesis{TaoChenLiangJiYuJiQiXueXiDeDuoChiDuChouYangWuRanMoNiJiQiShengChengMinGanXingFenXi,
  type = {{博士学位论文}},
  title = {{基于机器学习的多尺度臭氧污染模拟及其生成敏感性分析}},
  author = {{陶辰亮}},
  journal = {工程科技I辑;信息科技},
  address = {济南},
  doi = {10.27272/d.cnki.gshdu.2024.000608},
  urldate = {2025-08-22},
  abstract = {近地面臭氧(O3)污染会对人体健康和生态系统产生有害影响,是当前全球面临的重要大气环境问题之一。过去四十年,欧美诸国通过实施前体物减排政策有效地降低了大部分地区的O3污染。中国自2013年起,随着大气细颗粒物浓度(PM2.5)的持续降低,O3污染问题日益严重,已经进入了以多物种共存和多尺度关联为特征的区域性、复合型大气污染阶段,致使环境空气质量的持续改善越发困难。目前对O3、氮氧化物(NOx)、挥发性有机化合物(VOCs)和PM2.5之间的非线性生消关系缺乏全面理解,特别是PM2.5对O3生成敏感性的影响仍不清楚,这严重阻碍了对O3和PM2.5复合污染的协同控制。 本研究综合利用遥感、地面监测、气象等多源异构数据,构建了一个基于集成学习的O3关键前体物时间敏感性预测模型,用来识别不同时间尺度NO2浓度变化的关键因素;通过开发先进的近地面大气污染物浓度重构深度学习方法体系,建立了高时空分辨率、空间全覆盖的O3、PM2.5、HCHO和NO2浓度数据集;利用可解释的机器学习(ML)技术,优化了 O3光化学敏感性推断方法,精确地识别区域O3生成控制区;通过解耦气象和化学前体物对O3生成的影响,系统研究了不同尺度O3与NOx、VOCs和PM2.5之间的非线性关系。该研究为空气质量预报预警、大气污染防控决策和区域复合污染协同控制提供了理论依据和技术支撑,并对推进ML在大气化学研究中的应用与发展具有重要意义。 基于中国1609个空气质量监测站2014-2020年的数据,构建了国家尺度的O3前体物时间敏感性预测集成ML模型。该模型通过加权平均策略融合了耦合残差连接的循环神经网络、Transformer和极端梯度提升树模型的优势,在华中、华东和华北地区表现出较低的NO2浓度预测空间不确定性。识别不同时间尺度NO2浓度变化的驱动因素发现,大气化学过程对小时尺度预测更重要,而气象条件则显著影响日尺度预测。该模型可以在任何空气质量监测站实现NO2浓度快速稳定的预测,为污染事件预报提供有价值的参考。 基于可解释ML模型分析海口市2019到2020年PM2.5、不同排放源的VOCs、NOx和气象条件对O3生成的独立和协同影响后发现,在低PM2.5浓度下,O3和PM2.5呈线性正相关;而二次气溶胶含量较高的交通源PM2.5则抑制O3的生成。尽管中午时分交通源VOCs浓度最低,但对O3生成的贡献却最大。NOx与化石燃料燃烧源和工业源VOCs之间相对丰度的差异,导致这些VOCs对O3生成产生了相反的影响;并且PM2.5浓度的增加会改变并加强O3对不同源VOCs的响应。超出WHO空气质量指导值的O3重污染事件主要归因于化石燃料源VOCs浓度的减少以及交通和工业源VOCs浓度的增加,并且它们的影响会随PM2.5浓度增加而加剧。当前海口市O3生成处于VOC控制区,为了制定有效的区域O3污染防治政策,应优先考虑大幅度的减少工业和车辆源VOCs排放,以便抵消PM2.5共同消减时引起的O3对VOCs敏感性增强效应;此外,一旦PM2.5得到了有效的控制,应转而关注化石燃料燃烧源VOCs的减排。 开发基于Transformer模型的可动态感知时空隐藏信息的深度学习框架,估算山东省近地面大气污染物浓度。该模型对O3、NO2和PM2.5进行基于样本的十折交叉验证得到R2分别为0.96、0.92和0.95,表现出鲁棒的模型性能和空间梯度变化捕捉能力,明显优于基于单像素预测模式的ML模型。O3生成敏感性分析显示,非城市地区O3生成主要受NOx限制,而在城市地区对VOCs的敏感性增加。胶东半岛夏季O3生成处于``气溶胶抑制''控制区,较低的NOx浓度增强了颗粒物对HO2的非均相摄取,从而抑制O3生成。ML模拟表明,PM2.5浓度下降会增加O3对VOCs的敏感性,为了有效地缓解O3污染,需要实施更严格的VOCs减排措施。2020年,由于非城市地区PM2.5浓度下降较少,且受颗粒物的抑制效应更显著,导致O3浓度大幅下降,最终城市地区O3浓度超过了非城市地区。 通过深度学习模型重构了 2019-2023年全球大气污染物浓度,分析了当前全球大气污染及O3生成敏感性的时空动态演变,探讨了气象和化学因素对O3污染的影响,并揭示了区域减排和极端气候事件导致的O3生成控制区的变化。新冠疫情期间,O3对前体物的非线性响应导致全球O3浓度分布变化差异较大;中国中东部地区NO2、HCHO和PM2.5浓度显著降低,但是O3浓度上升。当前中国华北地区O3污染最为严重,但由于中国政府近来实施的的严格治理措施,O3浓度已呈现显著地下降趋势。剔除气象因素影响后,西亚、北美、中亚和南亚等地区的O3浓度呈现显著地增加趋势。在人口密集和发生极端气候事件的区域,夏季O3生成更易受VOCs或颗粒物的影响。野火排放显著的改变了大气中O3生成敏感性,燃烧排放的烟羽包含大量气溶胶,导致O3对颗粒物的敏感性显著增加,而随野火羽流抬升和扩散,O3光化学控制区在下风向转变为VOC限制。 本论文通过机器学习模拟多尺度臭氧污染,并系统分析O3-NOx-VOCs-PM2.5之间的复杂交互影响,揭示了基于SHAP的ML方法在解析大气二次污染形成机制中的应用潜力,强调了面对O3污染新挑战,迫切需要新的建模方法进行更深入的分析以理解其形成机制。为缓解极端气候事件导致的全球O3污染加剧,数据驱动的机器学习模型将为高效精确推断O3生成控制区提供潜在替代方法,从而为因地制宜的前体物减排战略的制定和实施提供支撑。},
  collaborator = {{王文兴} and {张庆竹}},
  langid = {chinese},
  school = {山东大学},
  keywords = {,/unread,O3,O3-NOx-VOCs-PM2.5},
  annotation = {major: 资源与环境\\
download: 102\\
CLC: TP181;X515\\
CNKICite: (\\
dbcode: CDFD\\
dbname: CDFDTEMP\\
filename: 1024042344.nh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/DXCZCRRF/陶辰亮 - 基于机器学习的多尺度臭氧污染模拟及其生成敏感性分析.pdf}
}

@article{WangLiHuaFenGuoMinDeYanJiuXianZhuangYuYingDuiCuoShi2025,
  title = {{花粉过敏的研究现状与应对措施}},
  author = {王丽 and 朱丽珍 and 沈效东 and 余泽龙 and 左龙 and 张世杰},
  year = {2025},
  journal = {现代园艺},
  volume = {48},
  number = {1},
  pages = {31--36},
  issn = {1006-4958},
  doi = {10.14051/j.cnki.xdyy.2025.01.064},
  abstract = {城市作为人类主要聚集地，部分植物因释放过敏花粉而诱发的过敏反应，成为威胁城市人口健康的障碍因子。花粉过敏发展成为一个广泛而复杂的研究领域，涉及花粉过敏原的分子特性、携带过敏原的颗粒的性质以及来源分布等多学科知识，了解空气传播花粉过敏原的趋势，对于花粉相关呼吸系统疾病在全球范围内的高流行率和社会经济影响具有重要意义。本研究梳理了国内外花粉过敏研究进展，分析了影响花粉过敏的因素及对人们健康产生的危害，进一步获得花粉过敏程度的评估方式。在此基础上，归纳出主要致敏植物及花粉过敏的流行趋势与花粉过敏的预防和治疗，指出了要估计城市植被和大气花粉浓度对变态反应者的健康危害，关键是建立高效、快速的监测系统和可靠的变态反应风险指标，以及对未来该领域研究方向提出展望，以期为我国城市健康发展提供科学指导，助力``健康中国''发展战略的实施。},
  langid = {chinese},
  lccn = {36-1287/S},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/F45N68U8/花粉过敏的研究现状与应对措施_王丽.pdf}
}

@phdthesis{WangQuanZengJiYuShenDuXueXiDeHuaFenZiDongJianCeSuanFaYanJiu2020,
  type = {{硕士学位论文}},
  title = {{基于深度学习的花粉自动检测算法研究}},
  author = {王全增},
  year = {2020},
  address = {北京},
  abstract = {伴随着城市化进程的发展,花粉过敏人群日益增多。花粉症已经成为季节性流行病。准确及时的花粉预报可以为花粉过敏患者的正常生活提供更好的保障。花粉检测是花粉预报的基础技术,其目的是在采集的花粉样本图片中准确地识别花粉颗粒。目前的花粉检测任务需要依靠有专业经验的研究人员在图片中手工标注花粉颗粒。这种费时费力的方式,无法满足花粉预报的要求。随着深度学习的快速发展,目标检测算法的精度得到很大提高。但复杂的网络结构会带来检测效率的降低。为了满足花粉预报准确性和实时性的要求,花粉检测算法需要在检测精度和检测效率之间取得良好的平衡。针对这一需求,开展了基于深度学习的花粉自动检测算法研究。具体内容如下:(1)构建了花粉自动检测数据集。由于缺少公开的花粉检测数据集,所以针对北京市两种主流的花粉采集方式构建了花粉检测数据集。该数据集包含了扫描电子显微镜下的花粉图片和光学显微镜下的花粉图片。通过对数据集的统计和测试,验证了该数据集符合自然环境下的花粉分布规律,可以较好的完成花粉检测算法的测试任务。(2)构建了基于特征融合的快速花粉检测算法。针对花粉数据集中小目标花粉比例较大的特点,通过特征融合的方式集合浅层特征和深层特征用于花粉检测。与此同时,本算法使用了一种结构简单的基础主干网络用于特征提取,以保证较小的模型规模和较高的检测效率。(3)构建了基于自注意力机制的花粉检测算法。针对花粉颗粒容易破裂畸形,易与背景融合的特点,添加了空间自注意力模块和通道自注意力模块。两个并联的自注意力模块可以捕捉图片的上下文信息,突破局部感受野的限制,提高花粉检测精度。本文首先针对缺少适用数据集的问题建立了符合自然分布规律的花粉检测数据集。然后使用深度学习的方法设计了一种快速花粉检测算法,在检测精度和检测效率之间取得了较好的平衡。在深入分析花粉数据集特点的基础上,本文针对性地使用特征融合和自注意力机制的方法,提高了花粉检测精度,并在构建的花粉数据集上验证了算法的有效性。},
  langid = {chinese},
  school = {北京工业大学},
  keywords = {,/unread},
  annotation = {citation: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/6AV4NS3S/基于深度学习的花粉自动检测算法研究_王全增.pdf}
}

@phdthesis{WangYanChaoWoGuoShuiNiXingYeJianWuJiangTanXieTongLuJingJiChengBenXiaoYiYanJiu2025,
  type = {{博士学位论文}},
  title = {{我国水泥行业减污降碳协同路径及成本效益研究}},
  author = {{王彦超}},
  year = {2025},
  journal = {工程科技I辑;经济与管理科学},
  address = {长春},
  doi = {10.27162/d.cnki.gjlin.2025.000040},
  urldate = {2025-08-22},
  abstract = {水泥行业是典型的高耗能、高排放行业,其排放的大气污染物和CO\textsubscript{2}会对地区环境及气候变化产生影响。我国作为世界最大的水泥生产国,水泥行业NOx、颗粒物排放占全国工业排放的17\%和21\%,CO\textsubscript{2}直接排放占到全国总排放总量的12\%-15\%,在2030年工业行业碳达峰和2035年美丽中国建设的战略背景下,面临着实现大气污染和碳排放控制的双重压力,亟需构建合理可行的减污降碳协同路径来应对这一挑战。鉴于此,本研究以减污降碳协同为切入点,通过协同技术筛选、协同路径设计及成本效益分析,提出了经济合理、技术可行的减污降碳协同路径,为我国应对大气污染防治与碳排放控制的双重挑战提供决策参考。本文主要研究内容及结论如下: （1）本文以全国1623条水泥熟料生产线为研究对象,更新完善了区域差异化的大气污染物排放因子,以生产线为基本单元计算了熟料和水泥生产环节大气污染物和CO\textsubscript{2}排放量,创新引入了汽车运输环节的大气污染物和CO\textsubscript{2}排放,构建了全流程大气污染物和CO\textsubscript{2}融合排放清单。研究表明,由于2018年以来我国水泥行业大气污染治理技术进步及部分省份排放标准的加严,2021年行业颗粒物、SO\textsubscript{2}、NOx排放因子均明显小于国家发布的排放因子,且重点区域省份明显小于非重点区域省份。熟料生产和汽车运输环节基本集中了水泥企业95\%左右大气污染物和CO\textsubscript{2}排放,是协同减排的重点环节;4000吨/日及以上熟料生产线大气污染物和CO\textsubscript{2}排放占总排放量的比例分别为67.9\%和71.4\%,是实施减污降碳协同的重点产能规模;安徽、广东、广西、云南、四川等熟料生产大省的大气污染物和CO\textsubscript{2}排放总量最大,是协同减排的重点省份。 （2）在融合排放清单研究的基础上,本文针对产能淘汰、燃烧系统改造、燃料替代、原料替代、末端超低排放治理、清洁运输改造、绿电替代、碳捕集、利用与封存（Carbon Capture,Utilization,and Storage,CCUS）等控制技术,建立了减污降碳协同度评价方法,评估了上述技术的减污降碳协同度,筛选了能够实现协同的技术。结果显示,产能淘汰、燃烧系统改造、燃料替代、末端超低排放治理、清洁运输改造等措施均能够实现大气污染物和CO\textsubscript{2}协同减排。其中,产能淘汰大气污染物和CO\textsubscript{2}减排比例均能够达到100\%,协同效益最好;燃烧系统改造、清洁运输改造减污降碳协同度评价值分别为11.7和5.6,均是偏向大气污染减排的协同技术;末端超低排放治理协同度高达204,减污与降碳成效相差较大。燃料替代在大气污染物和CO\textsubscript{2}协同减排方面存在不确定性,采用生物质、一般工业固体废物替代煤炭的条件下,可表现出SO\textsubscript{2}或VOCs与CO\textsubscript{2}的减排协同性。原料替代、绿电替代、CCUS技术则为非协同性技术,仅能减少CO\textsubscript{2}排放。 （3）根据减污降碳协同技术筛选结果,结合我国水泥行业中长期发展政策、大气污染防治和碳排放控制政策,设计了减污降碳的政策情景路径,在考虑政策执行弹性的基础上设计了政策加强情景路径,并开展两种情景路径下减排-环境-健康效益分析。结果显示,两种情景路径下我国水泥行业大气污染物排放总量相较2021年可分别减少55.3\%、73.3\%,非工业过程CO\textsubscript{2}排放分别减少38.0\%、73.0\%,大气污染物与CO\textsubscript{2}减排幅度总体相当,说明两种情景路径均能够取得较好的协同减排效果。大气污染物减排潜力主要来自云南、贵州、广西、广东、江西等非重点区域省份,而CO\textsubscript{2}减排潜力主要来自安徽、云南、四川、山东、河北等熟料生产大省。对于两种情景路径下的环境及健康效益,预计到2035年,水泥行业大气污染物排放对全国PM\textsubscript{2.5}年均浓度贡献将分别减少28.9\%、42.7\%,可能导致的过早死亡风险人数将分别减少14.2\%、20.8\%。 （4）基于减排-环境-健康效益分析,进一步测算了减污降碳协同经济效益,结合不同情景路径下成本测算,比较了经济效益费用比和减排效益费用比,提出了我国水泥行业减污降碳协同路径及分地区实施建议。研究发现,两种情景路径下减污降碳协同的经济效益费用比分别为1.5和1.9,总体都实现了经济效益大于成本投入。但在政策情景路径下辽宁、广东、湖北、福建、海南、重庆出现了成本投入大于经济效益的情况;政策加强情景路径下重点区域的北京、天津、河北、山东、河南、山西、陕西、江苏、浙江、安徽,以及非重点区域的西藏大气污染物和CO\textsubscript{2}减排效益费用比要低于政策情景路径。同时,不同措施减排效益费用比也存在较大差距,且受到社会经济、政策现状等的影响。综合考虑上述因素,最终选择政策加强情景路径作为减污降碳协同的主体路径,并融合政策情景路径中效益较好的措施要求,构建了更加经济合理、技术可行的减污降碳协同路径。最后,针对不同地区水泥行业减污降碳协同差异,提出重点区域省份重点推进产业结构调整、燃料替代、清洁运输改造,统筹推进绿电替代、CCUS等其他环节减污降碳,非重点区域省份重点推进大气污染深度治理及清洁运输改造,有序推进燃料替代及产业结构调整的建议。},
  collaborator = {{王宪恩}},
  langid = {chinese},
  school = {吉林大学},
  keywords = {,/unread},
  annotation = {major: 环境管理与环境经济\\
download: 1283\\
CLC: F426.71;X322\\
dbcode: CDFD\\
dbname: CDFDLAST2025\\
filename: 1025304679.nh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/JI4X3DZG/王彦超 - 2025 - 我国水泥行业减污降碳协同路径及成本效益研究.pdf}
}

@phdthesis{WangYaQunJiYuShuJuZengGuangHeTeZhengRongHeDeHuaFenXiBaoTuXiangShiBie2020,
  type = {{硕士学位论文}},
  title = {{基于数据增广和特征融合的花粉细胞图像识别}},
  author = {王亚群},
  year = {2020},
  address = {北京},
  abstract = {花粉细胞图像识别在空气检测、化石鉴定、蜂蜜质量控制、植物年代测定与跟踪等环境和医学领域扮演着重要角色。传统花粉细胞图像识别基于人工进行特征设计,并使用机器学习方法进行分类识别,不仅对从业人员的植物形态学知识与实践经验要求较高,而且通常需要复杂的特征工程。而基于卷积神经网络的花粉细胞图像识别能够避免复杂操作产生不可控的精度误差,实现花粉图像高效、可靠的分类识别。为此,本文开展基于卷积神经网络的花粉细胞图像识别研究,主要工作和贡献总结如下:(1)针对花粉细胞图像采集、成像困难造成的数据集规模较小问题,设计了一种基于数据增广的花粉细胞图像识别方法。在POLEN23E花粉细胞图像数据的基础上,加入三种分割花粉细胞图像数据集,并对POLEN23E数据集进行基于Mixup算法的统计学增广,构建了 MixupPOLEN23E+CPOELN23E等多个增广花粉细胞图像数据集。实验表明,MixupPOLEN23E+CPOELN23E增广图像集取得了较好识别效果,本文的数据增广方法将花粉细胞图像识别准确率平均提升3.4\%-4.6\%,有效弥补了样本量不足的缺陷。(2)针对花粉细胞图像识别困难问题,设计了一种基于特征融合的花粉细胞图像识别方法。一方面对花粉图像进行传统特征提取,构建基于词频的视觉词典,并与深度残差网络特征图进行级联融合,加强样本的特征学习。另一方面在网络中引入了 Focal Loss损失函数,减小了简单/困难样本识别差异。实验表明,基于特征融合的识别方法能有效改善花粉细胞图像特征提取质量,提高花粉细胞图像识别精度。},
  langid = {chinese},
  school = {北京邮电大学},
  keywords = {,/unread},
  annotation = {citation: 2},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/XTGPS88R/基于数据增广和特征融合的花粉细胞图像识别_王亚群.pdf}
}

@article{XiaoYuHuiChengShiZhiMinHuaFenJianCeJiDuiRenTiJianKangDeYingXiangYanJiuJinZhan,
  title = {{城市致敏花粉监测及对人体健康的影响研究进展}},
  author = {肖雨慧 and 韩立建 and 李伟峰 and 周伟奇 and 钱雨果 and 刘佳欣 and 朱孟郡},
  journal = {生态学杂志},
  pages = {1--15},
  issn = {1000-4890},
  doi = {10.13292/j.1000-4890.202510.050},
  abstract = {城市生态建设过程中对高致敏植物和致敏花粉问题的系统认知与管理不足，盲目引种或将导致城市致敏花粉问题日益严重。为了系统理解致敏花粉从致敏花粉植物到致敏花粉的影响过程，本文基于``格局-过程-效应''的多维视角综述了致敏花粉的源头及监测方法、大气中的运移特征，及其潜在的综合影响和风险评估方法。首先，梳理了气传致敏花粉的主要来源植物，总结了致敏花粉的监测技术；其次，围绕植物开花的生长发育阶段与环境因子的关系，总结了基于遥感和深度学习等技术的致敏植物花期识别方法；第三，概括了致敏花粉浓度的预测方法，辨析了盛花期和非盛花期两种截然不同的致敏花粉影响与运移模式，总结了致敏花粉的综合风险评估方法。最后，文章指出了当前在城市致敏花粉预测、运移模拟以及健康评估等方面存在的不足，并展望了未来研究方向和潜在的应用前景。本文对于提升城市花粉过敏防控水平、促进城市生态建设具有重要的参考意义。},
  langid = {chinese},
  lccn = {21-1148/Q},
  keywords = {,/unread},
  annotation = {中文核心期刊/北大核心: 是},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/UTE9QI4R/城市致敏花粉监测及对人体健康的影响研究进展_肖雨慧 (1).pdf}
}

@phdthesis{YangJiaJingJiYuFuJiXianWeiChengXiangDeShiShiHuaFenJianCeXiTongDeYanJiu2022,
  type = {{博士学位论文}},
  title = {{基于富集-显微成像的实时花粉监测系统的研究}},
  author = {杨家婧},
  year = {2022},
  address = {杭州},
  abstract = {近年来,随着花粉过敏人群的增加,人们对于知晓大气中花粉种类与浓度的需求正在逐渐上升。传统的花粉颗粒监测技术依赖长时间的采样和人工目视检测,整套流程耗时且需要专业技术人员参与,难以建立大范围的监测点,普及程度受限。颗粒物光学自动监测技术可通过单颗粒光学信号检测进行实时分析,然而该技术难以结合富集采样技术,对于低浓度的花粉颗粒检测准确度不高。基于静电、气流曳力等原理的捕集技术,结合花粉显微成像技术以及人工智能图像鉴别算法可实现高捕集率、高准确度、高自动化的低浓度花粉实时监测,具有应用于花粉大规模监测的潜力。第一章依据花粉富集和检测方法的不同,对花粉气溶胶监测领域的现状、发展与常见的商业化仪器进行了分类综述。对于先富集再检测的花粉分析技术,详细介绍了花粉被动与主动的富集采样方法、手动和自动检测方法,并分析了每种技术的优劣;对于流式花粉检测技术,主要介绍了利用单颗粒花粉的散射光和激光诱导荧光法实现花粉的鉴别和计数,以实现实时检测。最后,介绍了目前发展较为成熟的基于光学及成像原理的花粉自动识别系统。第二章发展了一种基于静电捕集和显微成像的全集成化花粉实时监测仪。该监测仪集成了无损进样模块、静电捕集模块、自动显微成像模块和自清洁模块,实现了花粉颗粒的定量引入、连续捕集、自动成像、机器视觉图像分析、捕集区域自清洁和循环使用。在三个数量级的大范围花粉浓度内,系统捕集效率均稳定25\%左右。此外,我们还提出了用于生成标准浓度的含花粉气体的方法,将花粉无损地传输到捕集模块,以便于仪器性能的定量化评估。与传统的花粉分析仪器相比,本监测仪实现了从捕集、成像到分析全过程的自动化,可实时获取花粉浓度,大大节省人工,提高了分析效率。第三章研制了基于静电-曳力捕集和多通道图像鉴别的自动花粉监测系统。基于空气倍增原理,构建了独立的花粉传输模块,实现了花粉颗粒的无损传输。在静电捕集的基础上增加了气流曳力捕集,将花粉颗粒的捕集效率提升至63\%,实现了高采样流量下的高效捕集;设计构建了多通道显微成像模块,可拍摄明场和两个不同波段的荧光通道花粉图片;通过荧光强度比值初步实现了花粉种类的鉴别,同时发展了基于深度学习的Alex Net图像分析算法,区分七种花粉颗粒的准确率达97\%以上。本监测仪实现了空气中花粉的无损传输、高效捕集、快速多通道显微成像以及人工智能图像分析的全流程自动化,可快速获取空气花粉信息,为构建大范围的花粉监测网络提供了设备与技术支撑。},
  langid = {chinese},
  school = {浙江大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BZW66V4V/基于富集-显微成像的实时花粉监测系统的研究_杨家婧.pdf}
}

@phdthesis{YaoJiangShangHaiHuXiDaoGuoMinRenQunShiKongFenBuBiaoZhengJiQuDongYinSuFenXi2024,
  type = {{硕士学位论文}},
  title = {{上海呼吸道过敏人群时空分布表征及驱动因素分析}},
  author = {姚江},
  year = {2024},
  address = {上海},
  abstract = {花粉过敏因其广泛的影响和严重的危害被世界卫生组织被列为21世纪重点研究和防治的三大疾病之一,引起了全球范围内的广泛关注。据统计,世界上大约30\%的人口都会受到大气中过敏性花粉的影响,其中约5亿人更是深受过敏性鼻炎(AR)的困扰。这不仅严重影响了人们的生活质量,还对经济财产和生命安全造成了威胁。因此,深入探索呼吸道过敏人数与花粉浓度之间的相关性,对于预防和治疗花粉过敏具有重要意义。 上海因其城市人口密集、致敏植被分布广泛的原因成为中国儿童过敏性疾病患病率最高的城市,本文以上海市为研究区对上海市2011-2020年过敏人群的时空分布特征及驱动因素进行分析。这里开展相关研究,有望为预防和治疗花粉过敏提供更为科学的依据和有效的方法。 本研究采用氯雷他定这一抗过敏药物来表征呼吸道过敏人数,同时以悬铃木的数量来表征花粉浓度。本文运用统计学方法对数据进行了初步分析,了解了氯雷他定销售量和悬铃木数量的基本分布和变化趋势。以相关性分析和地理加权回归分析方法探究氯雷他定销售量和悬铃木数量之间的相关性及其影响因素。本文的主要结论如下: (1)从时间维度来看,上海市2011-2020年10年间呼吸道过敏高发时期是5月,6-7月和9月,分别占全年过敏人数8.9\%、9.1\%、9\%、和9.2\%。且过敏期随着季节出现波动,夏季的过敏人数比春季多6.56\%,秋季比夏季多1.41\%,冬季人数相对春夏秋三季有所减少,但仍占过敏人群总数的22.6\%。2011年-2014年过敏人数从42万增加到66万,增长率高达57.45\%,2014年至2017年过敏人数大幅减少,整体数量减少了47.06\%。从空间维度来看,城市中心是呼吸道过敏易发区,城市中心周边其他地区是少发区。如呼吸道过敏人群杨浦区、徐汇、黄浦区、闵行区、浦东新区等中心地区共占74\%。而长宁区、嘉定区等地分布较少,长宁只占1\%。 (2)悬铃木在空间上的分布呈现出在徐汇区、普陀区、杨浦区、黄浦区、浦东新区等中心地区的集聚分布特征,占上海市悬铃木总数的58.5\%,而金山区、奉贤区、青浦区和嘉定区等地区只占6\%,说明在郊区及其他地区悬铃木随机分布或分散分布。氯雷他定销售量和悬铃木数量两者之间存在一定的正相关关系,即悬铃木数量的增加可能会导致氯雷他定销售量的上升,从而间接反映出呼吸道过敏人数的增加。 (3)通过构建地理加权回归模型,我们发现气温这一驱动因素呈现空间异质性,对过敏人群确实存在一定的正...},
  langid = {chinese},
  school = {上海师范大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/4ZKUWIV2/上海呼吸道过敏人群时空分布表征及驱动因素分析_姚江.pdf}
}

@phdthesis{ZhangManLinChengShiHuaFenZhiMinZhiWuGouChengFenBuJiQianZaiWeiHaiDuiBiYanJiu2022,
  type = {{硕士学位论文}},
  title = {{城市花粉致敏植物构成、分布及潜在危害对比研究}},
  author = {张曼琳},
  year = {2022},
  address = {重庆},
  abstract = {气传致敏花粉会引发过敏症,且可通过每年在花粉季的反复接触逐渐加重,危害人体健康,严重时甚至危害生命。随着城市化的快速发展,为了满足人们对绿地的需求,大量花粉致敏植物在城市中被广泛引种栽培,导致城市花粉症患者数量激增。 本研究以深圳市和重庆市为案例,基于建成区各144个样地的实地调查数据,分析花粉致敏植物的种类构成与时空分布,揭示不同城市花粉致敏植物的种类构成特点及其时空分布格局差异,并探寻其共性与规律,通过构建花粉浓度及花粉致敏危害潜力计算公式,预测城市中花粉致敏植物的潜在危害性。研究结果显示: (1)调查记录到深圳市花粉致敏植物47科89属114种,其中外来种占46.50\%。重庆市植物调查共记录花粉致敏植物54科104属130种,其中外来种占40.76\%。均以来自美洲的植物种居多,分别占47.22\%和46.43\%。两座城市花粉致敏植物均以开花观赏植物居多,占比均超过50.00\%。重庆花粉致敏植物种数以居住区绿地最多,乔灌草分别有45种、46种和52种。 (2)深圳市和重庆市花粉致敏植物盛花期均出现在春夏季,峰值分别出现在7月和5月,各占总种数的50.00\%和50.78\%。 (3)深圳市花粉致敏植物中,花粉致敏等级为I级的物种达97种,II级有15种,III级仅2种,即垂柳(Salix babylonica)和旱柳(Salix matsudana)。重庆市主城区花粉致敏植物中,花粉致敏等级为I级的物种达96种,II级有33种,III级仅2种,即垂柳和葎草(Humulus scandens)。两座城市均以I级轻度致敏等级花粉致敏植物为主。 (4)重庆花粉浓度潜力在4月份达到峰值,占全年总值62.38\%,深圳市花粉浓度潜力出现在5月,占全年总值的63.68\%。垂柳在深圳市和重庆市都位于乔木花粉浓度潜力前十,小蜡和苏铁在深圳市和重庆市都位于灌木花粉浓度潜力前十,马唐、狗牙根和车前均位于两个城市草本花粉浓度潜力的前十,且乔木和草本的花粉浓度潜力均高于灌木。 (5)重庆市二级重度危害样地仅出现在大渡口区春季花粉致敏样地,春季各行政区一级重度危害样地比其他季节数量更多,重庆市除冬季外各季节均有二级中度危害样地分布,但数量较少,全年均有一级中度和轻度危害等级样地分布。深圳市全年无二级重度危害样地出现,除冬季外各行政区均有一级重度危害样地分布,深圳市各行政区均分布有一级中度和轻度危害等级样地,但均无二级中度危害样地。重庆市和深圳...},
  langid = {chinese},
  school = {西南大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/2QNVGNMX/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳 (1).pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/LNYGXJ9I/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/QQQRBHG5/城市花粉致敏植物构成、分布及潜在危害对比研究_张曼琳.pdf}
}

@article{ZhangManLinChengShiHuaFenZhiMinZhiWuZhongLeiGouChengFenBuYuQianZaiWeiHaiPingGuYiShenZhenShiWeiLi2021,
  title = {{城市花粉致敏植物种类构成、分布与潜在危害评估------以深圳市为例}},
  author = {张曼琳 and 潘妮 and 赵娟娟 and 李明娟 and 江南},
  year = {2021},
  journal = {生态学报},
  volume = {41},
  number = {22},
  pages = {8746--8757},
  issn = {1000-0933},
  abstract = {气传致敏花粉会引发过敏症,且可通过每年在花粉季的反复接触逐渐加重,危害人体健康,严重时甚至危害生命。花粉致敏植物在城市中被广泛引种栽培,导致城市花粉症患者数量激增。以深圳市为案例,基于建成区600个样地的实地调查数据,分析花粉致敏植物的种类构成与时空分布,通过构建花粉浓度及花粉致敏危害潜力计算公式,评估花粉致敏危害潜力及其分布特点。结果显示:(1)调查记录到深圳市建成区花粉致敏植物46科92属186种,其中外来种占43.37\%,其中美洲、亚洲和大洋洲来源占国外外来种的81.00\%。花粉致敏植物种数以公园绿地最多,达126种。植物花粉致敏等级以I级为主,达154种。(2)花粉致敏植物的盛花期为春夏季,占全年累计开花种数的65.02\%。最高峰出现在8月,开花种数达92种。(3)花粉浓度潜力最高值也出现在8月,占全年总值的12.13\%。豆科和禾本科植物贡献比例较大,分别占乔木和草本总值的40.86\%和64.13\%。龙岗区花粉浓度潜力占比较高,占各季花粉量的26.06\%---29.42\%。(4)冬春两季各样地花粉致敏危害等级均不高,但夏秋季有些样地达到二级重度危害。罗湖区调查样地全年花粉致敏危害等级均较低,光明区在春夏秋季致敏危害等级皆较高。高危害等级样地主要出现在附属绿地及公园绿地。(5)花粉致敏植物防控措施情景模拟结果,去除主要花粉致敏植物种类比降低所有花粉致敏植物的花粉浓度潜力更有效。本研究为城市花粉致敏植物的管理、规划、养护和研究提供参考。},
  langid = {chinese},
  lccn = {11-2031/Q},
  keywords = {,/unread,No DOI found},
  annotation = {中文核心期刊/北大核心: 是},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/4W8DYGHN/张曼琳 等 - 2021 - 城市花粉致敏植物种类构成、分布与潜在危害评估——以深圳市为例.pdf}
}

@phdthesis{ZhangXinLianShengWuQiRongXiaoKuaiSuPingGuXiTongJiDaQiKeLiWuJiXingDuXingPingGuXiTongDeYanFa2020,
  type = {{博士学位论文}},
  title = {{生物气溶胶快速评估系统及大气颗粒物急性毒性评估系统的研发}},
  author = {{张新联}},
  year = {2020},
  address = {上海},
  urldate = {2025-08-22},
  collaborator = {{隋国栋}},
  langid = {chinese},
  school = {复旦大学},
  keywords = {,/unread},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/2F2UFPXX/张新联-生物气溶胶快速评估系统及大气颗粒物急性毒性评估系统的研发.pdf;/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/FJGMTANC/张敏 - 2025 - 大气边界层物理化学过程对半干旱区兰州盆地冬季污染的调控机制.pdf}
}

@phdthesis{ZhangXinYueJiYuTuXiangChuLiDeHuaFenShenDuTuXiangXiuZhengFangFaYanJiu2023,
  type = {{硕士学位论文}},
  title = {{基于图像处理的花粉深度图像修正方法研究}},
  author = {张心月},
  year = {2023},
  address = {呼和浩特},
  abstract = {花粉过敏症(即花粉症)在全球范围内均是常见病和多发病,并且花粉症的患病率近年呈现明显的上升趋势。花粉症会有鼻痒、流鼻涕、打喷嚏、眼痒等症状,甚至可以引起胸闷、憋气、哮喘等下呼吸道症状。现有的调查资料显示我国各城市和地区的花粉症患病率介于0.9\%到5\%之间,即使按照现有资料的最低发病率计算,我国花粉症的患者数量也大至千万以上。内蒙地区长有较多艾蒿、蒲公英等植物使得过敏季节飘散较多花粉,至花粉症患者呈逐年上升趋势。通过花粉三维形状重建了解花粉形状特征有助于研究人员对花粉症开展针对性的研究,同时对于花粉知识的科普用积极推动作用。到目前为止,研究者对深度图像修正进行了大量研究,但这些方法都不适用于花粉图像深度图估计。这些方法都是针对现实物体或场景的三维重现为目标,并且有专门的深度信息获取设备为辅助。但花粉图像是由花粉的电子反射信号生成的,不具备距离信息;同时,花粉样本个数较少,无法用基于深度学习的方法进行估计。本文以花粉三维形状重建为目标,研究基于图像处理的花粉图像深度信息修正方法。提出的基于图像处理的花粉图像深度信息修正算法,旨在保护花粉图像原本形状信息的基础上对其进行深度信息修正。首先,对花粉图像进行预处理并提出了一种花粉图像最长线提取方法。为避免噪声对结果图像的影响,对图像进行去噪处理接着获取花粉边缘信息;对边缘图像细化提取骨架边缘;最后计算骨架边缘的最长线轮廓信息。其次,提出了一种基于tanh函数的花粉深度图像修正方法。利用花粉图像多数是椭圆立体结构的特点对花粉深度图像进行修正。研究中假设花粉图像的中心点到边缘的灰度分布服从tanh函数的分布。修正原则为花粉图像中心位置距离观察点最近,因此对其中心位置进行大幅度灰度值修正。花粉边缘处距观察点最远,因此进行小幅度灰度值修正。本研究不考虑背景的深度信息,因此将图像背景置为黑色。根据以上原则使结果图像灰度值由花粉中心处向边缘递减,使图像在视觉上具备深度信息。接着,提出了两种基于Gamma函数的花粉深度图修正方法。第一个方法中,我们将基于tanh函数的花粉深度图像修正方法中的假设的服从tanh函数分布修改为服从Gamma函数的分布,以此为基础在原始图像中附加服从Gamma函数的灰度信息。使修正后图像的中心点变亮,边缘点基本保持不变。但通过第一个方法修正后的图像的花粉边缘部分还是比较亮,这不利于对花粉的三维形状重建。因此,我们对基于Gamma函数的花粉深度图修正方法一进行了...},
  langid = {chinese},
  school = {内蒙古工业大学},
  keywords = {,/unread,Gamma,tanh},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HAMU5AMM/基于图像处理的花粉深度图像修正方法研究_张心月.pdf}
}

@article{ZhouWeiQiChengShiLuDiHuaFenZhiMinYanJiuJinZhan2024,
  title = {{城市绿地花粉致敏研究进展}},
  author = {周伟奇 and 邓文萱 and 秦海明},
  year = {2024},
  journal = {生态学报},
  volume = {44},
  number = {23},
  pages = {10936--10952},
  issn = {1000-0933},
  doi = {10.20103/j.stxb.202401110097},
  abstract = {城市绿地中部分植物产生的气传花粉具有致敏性，可引发城市居民的花粉过敏症，因而城市绿地成为城市花粉致敏问题的主要来源和研究区域。随着城市化进程的推进，花粉过敏症越来越普遍，且存在长期致敏隐患，成为一个广受关注的城市居民健康问题。基于20世纪以来的花粉致敏研究热点和发展趋势，综述了城市绿地致敏花粉的监测、花粉致敏性的评价方法、花粉致敏的影响因素，以及花粉致敏风险预测。针对花粉致敏现象的成因、地区差异、危害与风险等分析评价不明朗，以及研究主题之间联系性不强等问题，系统归纳了致敏花粉的采集方法、种类与时空分布规律，阐明了评价花粉致敏性的各类方法与适用性条件，分析了影响花粉致敏的城市生态因子，提出了花粉致敏风险预警与防范措施，并指出了未来的发展方向。可为缓解花粉致敏问题提供研判基础、为防控致敏风险提供优化方案、为建立花粉致敏研究体系提供科学依据，进而降低花粉致敏对城市宜居性带来的不良影响。},
  langid = {chinese},
  lccn = {11-2031/Q},
  keywords = {,/unread},
  annotation = {中文核心期刊/北大核心: 是},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/BKHNLWVS/城市绿地花粉致敏研究进展_周伟奇.pdf}
}

@phdthesis{ZhuYanGangJiYuDuoTeZhengRongHeDeHuaFenTuXiangShiBieSuanFaYanJiu2018,
  type = {{硕士学位论文}},
  title = {{基于多特征融合的花粉图像识别算法研究}},
  author = {朱延刚},
  year = {2018},
  address = {南京},
  abstract = {在生物医学领域,花粉图像的识别分类不但具有重要的研究意义,而且市场应用前景广阔。传统的花粉分类工作主要借助人工在显微镜下完成,不仅对操作者的相关知识经验具有一定要求,而且分类过程缓慢,分类准确性较低。考虑到花粉图像有着和普通图像类似的轮廓纹理特征,随着近几年图像处理技术理论的迅速发展,通过提取花粉特征,完成对花粉图像识别分类成为一种有效的解决方法。由于原始花粉图像在采集的过程中受到光照、噪声污染等外部因素干扰,花粉图像的质量受到了不同程度的影响,导致识别率较低,因此要求提取花粉的特征具有较强的鲁棒性,提取的过程具有一定的实时性,最终可以得到较为理想的识别效果。本文就花粉图像的多特征提取融合进行研究,并从特征描述子和分类识别算法两个方面着手,既增强了算法的分类识别的准确度,也提高了算法的运行效率和实时性。主要研究的内容包括:(1)针对传统的特征提取算法往往只利用了单个花粉图像特征,提取的识别特征普遍存在抗噪能力不强、不具有不变性等缺点,本文运用传统机器学习的相关图像识别理论,提出了一种融合Zernike矩与BoF-SURF特征融合的花粉图像分类识别算法。首先对花粉图像的Zernike矩特征进行提取,然后提取改进后的SURF特征描述子,再对SURF特征进行K-Means聚类,构建加速鲁棒性特征包BoF-SURF,最后对这两种特征进行融合,由支持向量机SVM完成识别分类。由于Zernike矩和SURF描述子都具有不变性,针对花粉的尺度和旋转变化具有良好的鲁棒性,正确识别率也较高。(2)针对传统的花粉图像分类算法在提取特征的过程中,如何选取最有效的提取特征具有一定的复杂性,本文运用深度机器学习图像识别框架,提出了一种基于卷积神经网络CNN的特征融合花粉图像分类识别算法。本算法首先对花粉图像进行标准化处理,作为第一个训练网络的输入层图像数据矩阵,然后再对花粉图像进行方向梯度直方图HOG特征化处理,作为第二个训练网络的输入层图像数据矩阵,处理层结构为双层卷积-池化层,并且对各训练网络层的卷积矩阵参数进行了优化改进,以针对不同的数据矩阵进行更有效的特征抽取,然后输入到本算法增加的特征融合层,再经过全连接层的处理整合,最后通过输出层的softmax和损失函数完成对模型的训练,最终用于对花粉图像的分类。},
  langid = {chinese},
  school = {南京信息工程大学},
  keywords = {,/unread,BoF-SURF,HOG,Zernike},
  annotation = {citation: 5},
  file = {/Users/<USER>/Library/CloudStorage/OneDrive-个人/data/Zotero_data/sync/storage/HSNCQJ9Z/基于多特征融合的花粉图像识别算法研究_朱延刚.pdf}
}
