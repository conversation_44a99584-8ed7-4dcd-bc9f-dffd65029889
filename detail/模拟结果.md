# 海关模拟

## 1.案例介绍

海关几何模型如图1所示。模型由两个集装箱拼接而成，每个集装箱长30m，宽6m，高2.7m。整个模型关于中间平面（x=15m）对称，为简化计算模型只建立一半。海关被平面（z=3m）分割成检测人员区域和待检测的通关人员区域。检测员通过检测窗口为通过者检验，海关共有11个检测口，检测窗口下边缘据地板1m，尺寸为0.8m×0.8m，疫情期间所有窗口均提供检测服务。因此海关内部有11个工作人员和11个通关人员。需要注意的是所有人员均保持坐姿（人体皮肤面积1.25m2/人），桌子只布置在检测员一侧。采用隔板将每个工作窗口隔开以避免它们相互间的干扰。海关检测人员区域共有5台分体式空调器，空调器送风口高度为，送风口尺寸0.8m×0.075m，回风口尺寸0.8m×0.15m。检测期间分体式空调器全开向室内提供冷量。同时为了保证有足够的新风量，采用中央空调系统通过空调器下方的圆形风口向室内不断输送新风。圆形风口直径为0.3m，中心高度距地面2m。送入室内的空气首先经过检测口到达通关人员区域，然后通过通关人员区域的窗户排出室外。在通关人员区域放置气溶胶采样器收集空气中病毒样本，采样器高度为距地板1m，采样器进口尺寸0.03m×0.03m。

![1756104550741](image/模拟结果/1756104550741.png)

![1756104561298](image/模拟结果/1756104561298.png)![1756104568122](image/模拟结果/1756104568122.png)

图1 几何模型

## 2.边界条件

海关内部只有人体向环境散热。人体和环境总换热量为76W/人，人体对流换热量为20W/m2（约占人体总换热量的32%），其余热量均分给顶棚、侧墙、地板以简化辐射传热对室内气流的影响。模拟假设检测人员通过嘴巴一直吸气，通关人员通过嘴巴一直呼气。为了研究通风系统对通关人员呼出气溶胶的阻隔作用，采用co2气体对通关人员呼出飞沫进行示踪，设置人员呼出co2的体积分数为4%。具体边界条件设置如表1所示。

表1 边界条件设置

| **名称**           | **边界类型** | **参数**            |
| ------------------------ | ------------------ | ------------------------- |
| 空调器进风口             | 速度入口           | 3m/s,斜45°向下，温度24℃ |
| 空调器回风口             | 速度入口           | -1.5m/s                   |
| 圆形风口                 | 速度入口           | 3m/s,温度24℃             |
| 检测人员，被检查人员皮肤 | 定热流密度         | 20W/m2                    |
| 检测人员嘴巴             | 速度边界           | -1m/s                     |
| 通关人员嘴巴             | 速度边界           | 1m/s，温度37℃，co2浓度4% |
| 顶棚，侧墙，地板         | 定热流密度         | 4 W/m2                    |
| 中间平面（x=15m）        | 对称边界           |                           |
| 采样器                   | 速度入口           | 200L/min                  |
| 通关人员区域窗户         | 压力出口           | 0 Pa                      |
| 其他壁面                 | 绝热               | 0 W/m2                    |

## 3.模拟结果

对海关风速、温度、通关人员呼出气态污染物分布，通关人员呼出颗粒污染物分布进行分析。

### 3.1风速分布

图2显示了不同截面处速度分布及室内流线。由图2a截面（x=3.5, 5.7, 8, 10.3, 12.6 m）处速度分布云图可知，空调器送风与圆形风口送风气流发生碰撞并融合，一起朝着中间隔板运动，随后气流撞击到中间隔板向四周运动，部分向下的气流通过检测窗口进入通关人员区域，最后通过外窗排出室外。由图2b截面（y=1.4 m）处速度分布矢量图可知，检测窗口气流大部分由检测人员一侧流向通关人员一侧。气流通过检测窗口时由于截面变小，气流出现加速，通关人员头顶高度的风速约为1m/s左右。需要注意的是只有圆形风口正对的检测窗口发生明显的提速，而偏离送风口的区域没有出现这种现象。由图2c截面（x=10.3 m）处速度分布矢量图可知，气流运动状况与图2a相似，可以看出送风气流在与中间隔板碰撞后部分向上运动的气流在顶棚附近形成漩涡；与此同时，部分向下运动的气流通过检测窗口与人体发生碰撞并在通关人员前方形成漩涡。污染物可能会在漩涡处聚集，因此漩涡的形成对于污染物的排除是不利的。由图2d圆形风口送风流线图可知，室内具有良好的气流组织，送风能顺利通过检测窗口进入通关人员一侧。

![1756105575701](image/模拟结果/1756105575701.png)

a)截面（x=3.5, 5.7, 8, 10.3, 12.6 m）处速度分布云图

![1756105599623](image/模拟结果/1756105599623.png)

b) 截面（y=1.4 m）处速度分布矢量图

![1756105624886](image/模拟结果/1756105624886.png)

c) 截面（x= 10.3 m）处速度分布矢量图

![1756105652668](image/模拟结果/1756105652668.png)

d)圆形风口送风流线图

图2 速度分布图

### 3.2 温度分布

室内温度分布如图3所示。由图3a所示，室内温度场分布均匀，平均温度约为25℃，检人员一侧由于距离风口较近，温度略低于通关人员一侧（温差约为0.5℃）。图3b也显示两侧有轻微的温差，同时还可以看到头部上方(y=1.4m截面上)由于人体对流传热形成的热羽流。

![1756105668390](image/模拟结果/1756105668390.png)

a)截面（x=3.5, 5.7, 8, 10.3, 12.6 m）处温度分布云图

![1756105681829](image/模拟结果/1756105681829.png)

b) 截面（y=1.4 m）处温度分布云图

图3 速度分布图

### 3.3 污染物分布

污染物质量浓度分布如图4所示。由图4a污染物在截面x=10.3m处的浓度分布可以很清晰的看出气流对检测人员起到了保护作用。通关人员呼出的污染物被气流吹响人体后方，很难通过检测窗口进入检测人员区域。由图4b污染物浓度等值图可以看出，有些通关人员呼出的污染物可以通过检测窗口到达检测人员区域，特别是在偏离送风口的检测窗口处。

![1756105695058](image/模拟结果/1756105695058.png)

a) 截面（x= 10.3 m）处污染物浓度分布图

![1756105709295](image/模拟结果/1756105709295.png)

b) 污染物浓度等值图

图4 速度分布图

### 3.4 颗粒物示踪

进一步采用颗粒物对呼出污染物进行示踪，呼出颗粒物粒径为1um，颗粒密度为998kg/m3，质量流量为1e-10kg/s。忽略颗粒的蒸发过程。

由颗粒分布(图5)可以看出颗粒受到气流影响主要集中在通关人员一侧，较少的颗粒物通过检测窗口进入检测人员一侧。

![1756105734330](image/模拟结果/1756105734330.png)

图5 颗粒物分布图

### 3.5 捕获效率，渗透率

$$
采样器捕获效率  E_{s a m}  定义为采样口污染物的质量流量  M_{\text {sam }}  与呼出污染物质量流量  M_{\text {mou }}  的比值。比值越大说明捕集效率越高。同理定义通过检测窗口的污染物质量流量  M_{\text {win }}  与呼出污染物质量流量  M_{\text {mou }}  的比值为污染物渗透率  E_{\text {win }}  。  M_{\text {sam }}  为采样口污染物的质量流量， M_{\text {mou }}  为呼出污染物质量流量， M_{\text {win }}为通过检测窗口的污染物质量流量：
$$

采样器捕获效率：

$$
E_{\text {sam}}=\frac{M_{\text {sam}}}{M_{\text {mou}}}=\frac{1.4 e-6}{0.000344}=0.4 \%
$$

渗透率：

$$
E_{win}=\frac{M_{win}}{M_{mou}}=\frac{2.8 e-6}{0.000344}=0.8 \%
$$





基于您的CFD仿真模拟和实地采样验证研究，我建议制作以下可视化图表来全面呈现研究结果：

1. CFD仿真结果可视化图表
1.1 流场与温度场组合图
三维流线-温度场叠加图：展示气流路径与温度分布的耦合关系
多截面速度矢量对比图：不同x截面的速度分布对比，突出气流加速区域
检测窗口局部放大流场图：重点展示检测窗口处的气流特征
1.2 污染物传播可视化
CO₂浓度等值面三维图：立体展示污染物在空间中的分布
污染物浓度随时间演化动画：展示污染物从释放到稀释的动态过程
颗粒轨迹追踪图：1μm颗粒物的运动轨迹可视化
2. 采样效能定量分析图表
2.1 采样效率分析图
捕获效率vs渗透率对比柱状图：0.4% vs 0.8%的直观对比
采样点浓度分布热力图：不同采样位置的理论浓度分布
稀释倍数计算流程图：从源浓度到检测浓度的稀释过程
2.2 检测限临界分析图
浓度-检测概率关系曲线：展示1000 copies/ml检测限附近的检测成功率
样本浓度分布箱线图：理论预测浓度范围与检测限的关系
多次检测结果稳定性分析图：展示临界浓度下检测结果的变异性
3. 实地验证结果可视化
3.1 采样布局与结果分布图
采样点空间布局3D图：结合CFD流场显示采样点位置的合理性
航班-样本-结果关联矩阵图：展示5个航班的采样数量和阳性检出情况
阳性样本空间分布热力图：阳性检出位置与CFD预测高风险区域的对比
3.2 检测结果统计分析图
基因检测结果对比图：N基因vs E基因的检出频次对比
重复验证结果一致性分析：展示多次检测结果的稳定性
检测时间分布图：阳性样本的出峰时间分布特征
4. 仿真验证对比分析图表
4.1 预测vs实际结果对比
理论捕获效率vs实际检出率对比图：0.4%理论值与11.1%实际检出率的分析
CFD预测污染物分布vs实际阳性检出位置叠加图：验证仿真准确性
风险航班预测vs实际检出结果对比：重点航班筛选策略的验证
4.2 系统性能评估图
检测系统性能边界分析图：展示当前系统在性能极限附近工作的状态
成本-效益分析雷达图：采样成本、检测精度、时间效率等多维度评估
系统优化建议流程图：基于结果提出的改进方案
5. 综合分析与应用价值图表
5.1 技术路线图
CFD仿真指导采样的技术流程图：从建模到验证的完整技术路线
精准监测vs传统监测对比图：资源利用效率和检测效果的对比
多学科交叉融合示意图：CFD、采样工程、检测技术的协同作用
5.2 应用前景展望图
技术成熟度评估图：当前技术水平与未来发展潜力
应用场景扩展图：从海关检测到其他公共场所的应用可能性
检测限提升影响分析图：未来检测技术改进对系统性能的影响
6. 特色创新图表
6.1 "仿真-采样-检测"闭环验证图
三维立体流程图：展示从CFD仿真预测到实地验证成功的完整闭环
置信度分析图：不同环节的不确定性传播和最终结果可信度
6.2 临界检测现象深度分析图
检测限边界效应可视化：展示1000 copies/ml附近的检测行为特征
概率检测模型图：临界浓度下检测结果的概率分布模型
制作建议：
使用专业科学绘图软件：如Origin、MATLAB、Python (matplotlib/seaborn)或R语言
保持图表风格一致性：统一色彩方案、字体和布局风格
注重数据准确性：确保所有数值与原始数据完全一致
添加详细图注：每个图表都应有完整的标题、坐标轴标签和图例说明
考虑色盲友好：选择对色盲人群友好的配色方案
适当使用动画：对于时间演化过程，可制作GIF动画增强表达效果