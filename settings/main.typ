// 导入模板并应用全局设置
#import "../settings/template.typ": *
#import "../settings/toc.typ": *
#import "../settings/heading.typ": *
#import "../settings/cover.typ": *
#show: doc => setup(doc)

// ======================================================
// --- 封面 ---
// 临时禁用页眉和页脚，实现"无页码无页眉"
// --- 封面 ---
// 直接包含封面文件，它会处理自己的页面设置和分页
#include "../settings/cover.typ"
#pagebreak()

// ======================================================
// --- 目录 (无页眉，无页码) ---
#set page(
  header: none,  // 目录部分不需要页眉
  footer: none   // 目录部分不需要页码
)

#toc-title()
#custom-toc()
#pagebreak()


// ======================================================
// --- 摘要 (继续使用罗马数字页码，添加页眉) ---
#set page(
  header: [
    #set text(
      font: ("STIX Two Text", "Noto Serif CJK SC"),
      size: 10pt
    )
    #align(center)[复旦大学博士学位论文]
    #v(0.3em)
    #line(length: 100%, stroke: 0.5pt)
  ],
  footer: [
    #v(1cm)
    #align(center)[#context counter(page).display("I")]
  ]
)

// 重置页码计数器，从摘要开始计数（罗马数字第一页）
#counter(page).update(1)



#include "../chapters/abstract.typ"
#pagebreak()


// ======================================================
// --- 正文 (阿拉伯数字页码) ---
// 恢复模板中定义的默认页眉和页脚 (阿拉伯数字)，同样使用 context 表达式
#set page(
  header: [
    #set text(
      font: ("STIX Two Text", "Noto Serif CJK SC"),
      size: 10pt
    )
    #align(center)[复旦大学博士学位论文]
    #v(0.3em)
    #line(length: 100%, stroke: 0.5pt)
  ],
  footer: [
    #v(1cm)
    #align(center)[#context counter(page).display("1")]
  ]
)
// 再次重置页码计数器，正文从第 1 页开始
#counter(page).update(1)

// --- 导入各章节内容 ---
#include "../chapters/chapter01.typ"
#pagebreak()

#include "../chapters/chapter02.typ"
#pagebreak()


#include "../chapters/chapter03.typ"
#pagebreak()

#include "../chapters/chapter04.typ"
#pagebreak()

#include "../chapters/chapter05.typ"
#pagebreak()

// ======================================================
// --- 参考文献列表 (继续使用阿拉伯数字页码) ---
#pagebreak()
#include "../chapters/references.typ"
