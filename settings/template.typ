// ================================================================
//         论文样式模板 (Thesis Style Template)
// ================================================================

// --- 标题和目录样式已移至独立文件中 ---

// --- 标准标题样式（摘要、参考文献等） ---
#show heading.where(level: 1): it => {
  v(1.5em)
  align(center, text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    22pt,
    weight: "bold"
  )[#it.body])
  v(0.5em)
}

// --- 图表标题定义 (全局生效) ---
#let figure-counter = state("figure-counter", 0)
#let table-counter = state("table-counter", 0)

#let caption(kind, body) = {
  let counter = if kind == "table" { table-counter } else { figure-counter }
  counter.update(n => n + 1)
  let name = if kind == "table" { "表" } else { "图" }

  align(center)[
    set par(leading: 0.4em)
    text(
      font: ("STIX Two Text", "Noto Serif CJK SC"),
      11pt
    )[#name #counter.display("1.1"): #body]
  ]
}

// --- 参考文献样式 (符合GB/T 7714-2015标准) ---
#show bibliography: set text(
  font: ("STIX Two Text", "Noto Serif CJK SC"),
  size: 11pt
)

// --- 引用样式设置 (正文中的引用为上角标，符合中文学术规范) ---
#show cite: it => {
  // 设置引用为上角标样式，用中文逗号分隔多个引用
  super[#text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    size: 9pt,
    baseline: 0.2em
  )[#it]]
}


// --- 核心功能函数 (应用页面和正文基础样式) ---
#let setup(doc) = {
  // --- 全局字体与页面设置 ---
  set page(
    paper: "a4",
    margin: (top: 3cm, bottom: 2.5cm, left: 2.5cm, right: 2.5cm),
    // --- 页眉设置 ---
    header: [
      #set text(
        font: ("STIX Two Text", "Noto Serif CJK SC"),
        size: 10pt
      )
      #align(center)[复旦大学博士学位论文]
      #v(0.3em)
      #line(length: 100%, stroke: 0.5pt)
    ],
    // --- 默认页脚设置 ---
    // 使用 context 表达式包装需要上下文的函数调用
    footer: [
      #v(1cm)
      #align(center)[
        #context counter(page).display("1")
      ]
    ]
  )
  // 正文设置：宋体、小四号、行距20磅、首行缩进3em
  set text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    size: 12pt,
  )
  // 设置行距为固定20磅，首行缩进3em
  set par(justify: true, leading: 20pt, first-line-indent: 2em)
  
  // --- 标题编号已在 heading.typ 中处理 ---

  // --- 将设置应用到文档 ---
  doc
}
