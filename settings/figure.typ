// ================================================================
//                   全局图片设置
// ================================================================

// --- 全局图片计数器 ---
#let figure-counter = counter("global-figure")

// --- 设置图片的全局编号格式 ---
#let setup-figures() = {
  // 这个函数现在为空，因为我们使用自定义计数器
}

// 便捷的图片插入函数
#let img(
  path,
  caption: "",
  width: auto,
  height: auto,
  fit: "contain"
) = {
  // 步进图片计数器
  figure-counter.step()

  // 获取当前章节号和图片序号
  let chapter-num = context counter(heading).display("1")
  let figure-num = context figure-counter.display("1")

  // 显示图片
  align(center)[
    #image(path, width: width, height: height, fit: fit)
    #v(0.5em)
    #text(size: 10pt, weight: "bold")[
      图 #chapter-num - #figure-num：#caption
    ]
    #v(0.5em)
  ]
}

// 带子图标记的并排图片函数（图片左上角显示(a)(b)标记）
#let side-by-side-figures(
  img1-path,
  img2-path,
  caption: "",  // 主标题
  width: 100%,
  height: auto,
  gutter: 1em,
  fit: "contain"
) = {
  // 步进图片计数器
  figure-counter.step()

  // 获取当前章节号和图片序号
  let chapter-num = context counter(heading).display("1")
  let figure-num = context figure-counter.display("1")

  align(center)[
    #grid(
      columns: 2,
      gutter: gutter,
      // 左图 - 带(a)标记
      box[
        #place(
          top + left,
          dx: 5pt,
          dy: 5pt,
          rect(
            fill: white.transparentize(20%),
            radius: 3pt,
            inset: 3pt,
            text(size: 9pt, weight: "bold")[(a)]
          )
        )
        #image(img1-path, width: width, height: height, fit: fit)
      ],
      // 右图 - 带(b)标记
      box[
        #place(
          top + left,
          dx: 5pt,
          dy: 5pt,
          rect(
            fill: white.transparentize(20%),
            radius: 3pt,
            inset: 3pt,
            text(size: 9pt, weight: "bold")[(b)]
          )
        )
        #image(img2-path, width: width, height: height, fit: fit)
      ]
    )
    #v(0.5em)
    #text(size: 10pt, weight: "bold")[
      图 #chapter-num - #figure-num：#caption
    ]
    #v(0.5em)
  ]
}

// 简化版并排图片函数（每个图独立编号）
#let two-images(path1, caption1, path2, caption2, height: 8cm) = {
  grid(
    columns: 2,
    gutter: 1em,
    img(path1, caption: caption1, height: height),
    img(path2, caption: caption2, height: height)
  )
}

// 三张图片并排函数（图片左上角显示(a)(b)(c)标记）
#let three-figures(
  img1-path,
  img2-path,
  img3-path,
  caption: "",  // 主标题
  width: 100%,
  height: auto,
  gutter: 0.8em,
  fit: "contain"
) = {
  // 步进图片计数器
  figure-counter.step()

  // 获取当前章节号和图片序号
  let chapter-num = context counter(heading).display("1")
  let figure-num = context figure-counter.display("1")

  align(center)[
    #grid(
      columns: 3,
      gutter: gutter,
      // 第一张图 - 带(a)标记
      box[
        #place(
          top + left,
          dx: 5pt,
          dy: 5pt,
          rect(
            fill: white.transparentize(20%),
            radius: 3pt,
            inset: 3pt,
            text(size: 9pt, weight: "bold")[(a)]
          )
        )
        #image(img1-path, width: width, height: height, fit: fit)
      ],
      // 第二张图 - 带(b)标记
      box[
        #place(
          top + left,
          dx: 5pt,
          dy: 5pt,
          rect(
            fill: white.transparentize(20%),
            radius: 3pt,
            inset: 3pt,
            text(size: 9pt, weight: "bold")[(b)]
          )
        )
        #image(img2-path, width: width, height: height, fit: fit)
      ],
      // 第三张图 - 带(c)标记
      box[
        #place(
          top + left,
          dx: 5pt,
          dy: 5pt,
          rect(
            fill: white.transparentize(20%),
            radius: 3pt,
            inset: 3pt,
            text(size: 9pt, weight: "bold")[(c)]
          )
        )
        #image(img3-path, width: width, height: height, fit: fit)
      ]
    )
    #v(0.5em)
    #text(size: 10pt, weight: "bold")[
      图 #chapter-num - #figure-num：#caption
    ]
    #v(0.5em)
  ]
}

// 自定义网格图片布局（图片左上角显示标记）
#let grid-figures(
  images,  // 数组：[path1, path2, path3, ...]
  caption: "",  // 主标题
  columns: 2,
  width: 100%,
  height: auto,
  gutter: 1em,
  fit: "contain"
) = {
  let letters = ("a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l")

  // 步进图片计数器
  figure-counter.step()

  // 获取当前章节号和图片序号
  let chapter-num = context counter(heading).display("1")
  let figure-num = context figure-counter.display("1")

  align(center)[
    #grid(
      columns: columns,
      gutter: gutter,
      ..images.enumerate().map(((index, path)) =>
        box[
          #place(
            top + left,
            dx: 5pt,
            dy: 5pt,
            rect(
              fill: white.transparentize(20%),
              radius: 3pt,
              inset: 3pt,
              text(size: 9pt, weight: "bold")[
                (#letters.at(index))
              ]
            )
          )
          #image(path, width: width, height: height, fit: fit)
        ]
      )
    )
    #v(0.5em)
    #text(size: 10pt, weight: "bold")[
      图 #chapter-num - #figure-num：#caption
    ]
    #v(0.5em)
  ]
}
